"use strict";const e=require("../../common/request.js");exports.generateSignedAgreement=function(t,r){return e.request({url:`/tran/userCommission/signComAgreement/${t}`,method:"post",param:r})},exports.getAgreement=function(t){return e.request({url:"/tran/userCommission/getAgreement/"+t,method:"get"})},exports.listAgreements=function(){return e.request({url:"/tran/userCommission/listAgreements",method:"get"})},exports.updateComStatus=function(t){return e.request({url:"/tran/userCommission/confirmSignComAgreement",method:"post",param:t})};
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/mine/userCommissionAgreement.js.map
