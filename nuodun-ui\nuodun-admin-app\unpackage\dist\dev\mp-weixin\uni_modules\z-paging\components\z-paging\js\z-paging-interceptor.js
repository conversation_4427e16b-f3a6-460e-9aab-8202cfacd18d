"use strict";const n="Query",t="FetchParams",e="FetchResult",a="Language2Local";function u(){return getApp()}function c(){return u()&&u().globalData}function r(n,t){try{setTimeout((function(){c()&&(u().globalData[`zp_handle${n}Callback`]=t)}),1)}catch(e){}}function o(n){return c()?u().globalData[`zp_handle${n}Callback`]:null}const l={handleQuery:function(t){return r(n,t),this},_handleQuery:function(t,e,a,u){const c=o(n);return c?c(t,e,a,u):[t,e,a]},handleFetchParams:function(n){return r(t,n),this},_handleFetchParams:function(n,e){const a=o(t);return a?a(n,e||{}):{pageNo:n.pageNo,pageSize:n.pageSize,...e||{}}},handleFetchResult:function(n){return r(e,n),this},_handleFetchResult:function(n,t,a){const u=o(e);return u&&u(n,t,a),!!u},handleLanguage2Local:function(n){return r(a,n),this},_handleLanguage2Local:function(n,t){const e=o(a);return e?e(n,t):t}};exports.interceptor=l;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/js/z-paging-interceptor.js.map
