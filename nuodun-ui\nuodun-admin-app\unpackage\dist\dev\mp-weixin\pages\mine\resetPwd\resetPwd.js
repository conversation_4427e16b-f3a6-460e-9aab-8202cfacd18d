"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/user.js"),s=require("../../../common/validate.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../../components/custom-nav/custom-nav.js")+(()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const a={__name:"resetPwd",setup(a){const{proxy:r}=e.getCurrentInstance(),n=r.toast,t=e.ref(!0),i=e.ref(!0),l=e.ref(!0),u=e.reactive({oldPassword:"",newPassword:"",confirmPassword:""}),d=()=>{u.oldPassword?s.checkPwd(u.newPassword)&&(u.confirmPassword==u.newPassword?o.resetUserPwd(u.oldPassword,u.newPassword).then((o=>{n.show("修改成功"),setTimeout((function(){e.index.redirectTo({url:"/pages/mine/mine"})}),800)})):n.show("两次输入的密码不一致")):n.show("请填写旧密码")};return e.onLoad((()=>{})),(o,s)=>({a:e.p({title:"修改密码",showLeft:!0,path:"/pages/tabbar/tabbar?activeTab=mine"}),b:t.value,c:u.oldPassword,d:e.o((e=>u.oldPassword=e.detail.value)),e:e.p({type:t.value?"eye-slash":"eye",size:"20",color:"#666"}),f:e.o((e=>t.value=!t.value)),g:i.value,h:u.newPassword,i:e.o((e=>u.newPassword=e.detail.value)),j:e.p({type:i.value?"eye-slash":"eye",size:"20",color:"#666"}),k:e.o((e=>i.value=!i.value)),l:l.value,m:u.confirmPassword,n:e.o((e=>u.confirmPassword=e.detail.value)),o:e.p({type:l.value?"eye-slash":"eye",size:"20",color:"#666"}),p:e.o((e=>l.value=!l.value)),q:e.o(d),r:e.gei(o,"")})}};wx.createPage(a);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mine/resetPwd/resetPwd.js.map
