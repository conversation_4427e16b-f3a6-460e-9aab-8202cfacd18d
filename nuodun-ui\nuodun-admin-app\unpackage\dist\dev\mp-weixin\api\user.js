"use strict";const e=require("../common/request.js");exports.resetUserPwd=function(r,s){const t={oldPassword:r,newPassword:s};return e.request({url:"/system/user/profile/updatePwd",method:"PUT",params:t})},exports.resetUserPwdByCode=function(r,s,t,o){const u={phoneNumber:r,verCode:s,newPassword:t,verType:o};return e.request({url:"/system/user/profile/updatePwdByVerCode",method:"PUT",params:u})};
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/user.js.map
