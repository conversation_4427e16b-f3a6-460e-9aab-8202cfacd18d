"use strict";const e=require("./vendor.js"),o=require("./toast.js"),n=require("./request.js");function s(n){return new Promise(((s,t)=>{e.index.chooseImage({count:n.count,sizeType:["compressed"],sourceType:n.sourceType,success:e=>{const i=e.tempFilePaths.map(((o,n)=>{const s=e.tempFiles[n];return{name:s.name||`image_${Date.now()}_${n}.jpg`,size:s.size||0,type:s.type||"image/jpeg",path:o}})).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==i.length?s(i):t(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:608","选择图片失败:",o),t(new Error("选择图片失败"))}})}))}function t(n){return new Promise(((s,t)=>{e.index.chooseMessageFile({count:n.count,type:"all",success:e=>{const i=e.tempFiles.map((e=>({name:e.name,size:e.size,type:e.type||"",path:e.path}))).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==i.length?s(i):t(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:647","选择文件失败:",o),t(new Error("选择文件失败"))}})}))}exports.chooseAndUploadFile=function(o={}){const i={count:1,maxSize:10,allowedTypes:["image","file"],acceptTypes:null,sourceType:["album","camera"],uploadData:{fileBizType:"common_file_upload"},uploadUrl:"/system/fileRecord/uploadByFile",onChoose:null,onProgress:null,onSuccess:null,onFail:null,onComplete:null,autoUpload:!0,showLoading:!0,...o};return new Promise(((o,l)=>{let r=null;const a=i.allowedTypes.includes("image"),p=i.allowedTypes.some((e=>"image"!==e));if(r=a&&!p?s:t,!r){const e=new Error("当前平台不支持文件选择");return i.onFail&&i.onFail(e),i.onComplete&&i.onComplete(!1),void l(e)}r(i).then((s=>{if(i.onChoose){if(!1===i.onChoose(s))return void o({files:s,uploaded:!1})}i.autoUpload?function(o,s){return new Promise(((t,i)=>{if(!o||0===o.length)return void i(new Error("没有文件需要上传"));const l=o.map(((o,t)=>function(o,s,t){return new Promise(((i,l)=>{s.showLoading&&e.index.showLoading({title:`上传中... (${t+1})`});const r={url:s.uploadUrl,filePath:o.path,name:"file",formData:s.uploadData,success:n=>{if(s.showLoading&&e.index.hideLoading(),200===n.code&&n.data){const e={...o,...n.data,uploaded:!0,uploadTime:(new Date).getTime()};s.onProgress&&s.onProgress({file:e,index:t,progress:100,status:"success"}),i(e)}else{const e=new Error(n.msg||"上传失败");e.file=o,e.index=t,s.onProgress&&s.onProgress({file:o,index:t,progress:0,status:"error",error:e}),l(e)}},fail:n=>{s.showLoading&&e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:743","上传文件失败:",n);const i=new Error("上传失败");i.file=o,i.index=t,i.originalError=n,s.onProgress&&s.onProgress({file:o,index:t,progress:0,status:"error",error:i}),l(i)}},a=n.upload(r);a&&a.onProgressUpdate&&s.onProgress&&a.onProgressUpdate((e=>{s.onProgress({file:o,index:t,progress:e.progress,status:"uploading"})}))}))}(o,s,t)));Promise.all(l).then((e=>{s.onSuccess&&s.onSuccess(e),s.onComplete&&s.onComplete(!0),t(e)})).catch((e=>{s.onFail&&s.onFail(e),s.onComplete&&s.onComplete(!1),i(e)}))}))}(s,i).then((e=>{o({files:s,uploadResults:e,uploaded:!0})})).catch((e=>{i.onFail&&i.onFail(e),i.onComplete&&i.onComplete(!1),l(e)})):o({files:s,uploaded:!1})})).catch((e=>{i.onFail&&i.onFail(e),i.onComplete&&i.onComplete(!1),l(e)}))}))},exports.downloadAndOpenFile=function(n,s={}){const t={onSuccess:null,onFail:null,onComplete:null,loadingText:"文件下载中...",successText:"文件已保存",failText:"文件下载失败",autoOpen:!0,fileName:"",...s};return new Promise(((s,i)=>{if(!n){const e=new Error("没有可下载的文件");return t.onFail&&t.onFail(e),t.onComplete&&t.onComplete(!1),void i(e)}e.index.showLoading(t.loadingText),e.index.downloadFile({url:n,success:n=>{200===n.statusCode?e.index.saveFile({tempFilePath:n.tempFilePath,success:n=>{e.index.hideLoading(),t.successText&&o.toast.show(t.successText),t.autoOpen?e.index.openDocument({filePath:n.savedFilePath,success:()=>{t.onSuccess&&t.onSuccess(n),t.onComplete&&t.onComplete(!0),s(n)},fail:e=>{o.toast.show("无法打开此类型文件"),t.onSuccess&&t.onSuccess(n),t.onComplete&&t.onComplete(!0),s(n)}}):(t.onSuccess&&t.onSuccess(n),t.onComplete&&t.onComplete(!0),s(n))},fail:n=>{e.index.hideLoading(),o.toast.show("文件保存失败"),t.onFail&&t.onFail(n),t.onComplete&&t.onComplete(!1),i(n)}}):(e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:950","文件下载失败",n),o.toast.show(t.failText),t.onFail&&t.onFail(n),t.onComplete&&t.onComplete(!1),i(new Error(t.failText)))},fail:n=>{e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:961","文件下载失败",n),o.toast.show(t.failText),t.onFail&&t.onFail(n),t.onComplete&&t.onComplete(!1),i(n)}})}))},exports.getFileType=function(e){if(!e)return"file";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"image":["mp4","avi","mov","wmv","flv","3gp"].includes(o)?"video":["pdf","doc","docx","xls","xlsx","ppt","pptx","txt"].includes(o)?"document":"file"},exports.getFileTypeIcon=function(e){if(!e)return"📄";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"🖼️":"pdf"===o?"📕":["doc","docx"].includes(o)?"📘":["xls","xlsx"].includes(o)?"📗":["ppt","pptx"].includes(o)?"📙":"📄"},exports.validateMaterialFileType=function(o,n=null){const s=function(o,n=null){if(o&&o.includes(".")){const n=o.toLowerCase().split(".").pop();return e.index.__f__("log","at common/fileUtils.js:217","从文件名获取扩展名:",{fileName:o,ext:n}),n}if(n&&n.includes(".")){const o=n.toLowerCase().split(".").pop();return e.index.__f__("log","at common/fileUtils.js:224","从文件路径获取扩展名:",{filePath:n,ext:o}),o}return e.index.__f__("log","at common/fileUtils.js:228","无法获取文件扩展名:",{fileName:o,filePath:n}),null}(o,n);return!!s&&["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(s)};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/fileUtils.js.map
