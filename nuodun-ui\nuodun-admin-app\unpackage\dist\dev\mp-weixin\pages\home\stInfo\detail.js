"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/home/<USER>"),a=require("../../../common/utils.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("dict-tag"))()}Math;const s={__name:"detail",setup(s){const{proxy:o}=e.getCurrentInstance(),r=o.toast,{t_st_deca_type:u,sys_user_sex:n,school_study_grade:l}=o.useDict("t_st_deca_type","sys_user_sex","school_study_grade"),c=e.ref(""),d=e.ref({});return e.onLoad((e=>{e.stId&&(c.value=e.stId,(async()=>{try{const e=await t.getStInfoDetail(c.value);200===e.code&&(d.value=e.data)}catch(e){r.show("获取详情失败")}})())})),(t,s)=>({a:e.p({title:"学生详情",showLeft:!0,path:"/pages/home/<USER>/index"}),b:e.t(d.value.stName),c:e.t(e.unref(a.maskPhone)(d.value.stPhone)),d:e.t(e.unref(a.maskIdCard)(d.value.cerCardNo)),e:e.p({options:e.unref(u),value:d.value.stDecaType}),f:e.p({options:e.unref(n),value:d.value.stSex}),g:e.t(d.value.stSchoolName||"-"),h:e.t(d.value.stMajorName||"-"),i:e.p({options:e.unref(l),value:d.value.stGradeName}),j:e.t(d.value.address),k:e.t(d.value.customerBase||"-"),l:e.t(e.unref(a.parseTime)(d.value.stObtainTime,"{y}-{m}-{d}")),m:e.gei(t,"")})}};wx.createPage(s);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/detail.js.map
