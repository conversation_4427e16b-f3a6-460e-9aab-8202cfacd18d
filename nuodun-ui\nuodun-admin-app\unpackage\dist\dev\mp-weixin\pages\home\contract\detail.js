"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/home/<USER>"),a=require("../../../common/utils.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("dict-tag"))()}Math;const n={__name:"detail",setup(n){const{proxy:l}=e.getCurrentInstance(),s=l.useDict("t_st_deca_type"),r=e.ref(null),u=e.ref(null),i=e.ref("installment");e.onLoad((e=>{e.id&&(r.value=e.id,o())}));const o=async()=>{try{const e=await t.getContractDetail(r.value);200===e.code&&(u.value=e.data)}catch(e){l.toast.show("获取详情失败")}},m=e=>{i.value=e};return(t,n)=>e.e$1({a:e.p({title:"合同详情",showLeft:!0,path:"/pages/home/<USER>/index"}),b:u.value},u.value?e.e$1({c:e.t(u.value.stName),d:e.t(u.value.orderId),e:e.t(u.value.contractAmount),f:e.p({options:e.unref(s).type.t_st_deca_type,value:u.value.stContractType}),g:e.t(e.unref(a.parseTime)(u.value.signDate,"{y}-{m}-{d}")),h:e.t(u.value.createTime),i:"installment"===i.value?1:"",j:e.o((e=>m("installment"))),k:"renewal"===i.value?1:"",l:e.o((e=>m("renewal"))),m:"installment"===i.value},"installment"===i.value?e.e$1({n:u.value.installmentList&&u.value.installmentList.length>0},u.value.installmentList&&u.value.installmentList.length>0?{o:e.f(u.value.installmentList,((t,n,l)=>({a:e.t(t.phaseNum),b:e.t(t.totalPhaseNum),c:e.t(t.dueAmount),d:e.t(t.paidAmount||"0.00"),e:e.t(e.unref(a.parseTime)(t.dueDate,"{y}-{m}-{d}")||"-"),f:e.t(e.unref(a.parseTime)(t.paidDate,"{y}-{m}-{d}")||"-"),g:e.t("S"===t.orderStatus?"已缴费":"未缴费"),h:"S"===t.orderStatus?1:"",i:"W"===t.orderStatus?1:"",j:n})))}:{}):{},{p:"renewal"===i.value},"renewal"===i.value?e.e$1({q:u.value.renewList&&u.value.renewList.length>0},u.value.renewList&&u.value.renewList.length>0?{r:e.f(u.value.renewList,((t,n,l)=>({a:e.t(t.renewNum||n+1),b:e.t(t.renewAmount),c:e.t(e.unref(a.parseTime)(t.paidDate,"{y}-{m}-{d}")||"-"),d:n})))}:{}):{}):{},{s:e.gei(t,"")})}},l=e._export_sfc(n,[["__scopeId","data-v-b6fdc1aa"]]);wx.createPage(l);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/detail.js.map
