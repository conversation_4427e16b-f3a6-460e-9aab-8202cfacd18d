"use strict";const t=require("../../../../common/vendor.js");class e{constructor(e,i){this.options=e,this.animation=t.index.createAnimation({...e}),this.currentStepAnimates={},this.next=0,this.$=i}_nvuePushAnimates(t,e){let n=this.currentStepAnimates[this.next],s={};if(s=n||{styles:{},config:{}},i.includes(t)){s.styles.transform||(s.styles.transform="");let i="";"rotate"===t&&(i="deg"),s.styles.transform+=`${t}(${e+i}) `}else s.styles[t]=`${e}`;this.currentStepAnimates[this.next]=s}_animateRun(t={},e={}){let i=this.$.$refs.ani.ref;if(i)return new Promise(((n,s)=>{nvueAnimation.transition(i,{styles:t,...e},(t=>{n()}))}))}_nvueNextAnimate(t,e=0,i){let n=t[e];if(n){let{styles:s,config:a}=n;this._animateRun(s,a).then((()=>{e+=1,this._nvueNextAnimate(t,e,i)}))}else this.currentStepAnimates={},"function"==typeof i&&i(),this.isEnd=!0}step(t={}){return this.animation.step(t),this}run(t){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof t&&t()}),this.$.durationTime)}}const i=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];i.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((t=>{e.prototype[t]=function(...e){return this.animation[t](...e),this}})),exports.createAnimation=function(t,i){if(i)return clearTimeout(i.timer),new e(t,i)};
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-transition/components/uni-transition/createAnimation.js.map
