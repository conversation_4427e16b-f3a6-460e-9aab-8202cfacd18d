"use strict";const e=require("../../../../common/vendor.js"),t=require("../../../../api/mine/userCommissionAgreement.js"),a={components:{ZPaging:()=>"../../../../uni_modules/z-paging/components/z-paging/z-paging.js"},setup(){const{proxy:a}=e.getCurrentInstance(),r=a.toast,n=e.ref(null),s=e.ref([]),o=e.ref(0);return e.onLoad((()=>{const t=e.index.getSystemInfoSync();o.value=t.statusBarHeight+50})),{paging:n,agreements:s,navHeight:o,queryAgreementList:(a,s)=>{t.listAgreements().then((e=>{if(200===e.code&&e.data){const t=e.data.map((e=>({...e,confirmStatus:e.confirmStatus||"0",comStatus:e.comStatus||"0",agreementStartDate:e.agreementStartDate,agreementEndDate:e.agreementEndDate,renewalDate:e.renewalDate,perAgreementCode:e.perAgreementCode||e.id}))).sort(((e,t)=>t.id-e.id));n.value.complete(t)}else n.value.complete(!1),r.show("获取合同信息失败")})).catch((t=>{e.index.__f__("error","at pages/mine/agreements/chan/agreementList.vue:160","获取协议列表失败:",t),n.value.complete(!1),r.show("获取合同信息失败")}))},getComStatusText:e=>{switch(e){case"0":return"无";case"1":return"待签名确认";case"2":return"已签名确认";case"3":return"自主退回";case"4":return"平台驳回";default:return"未知状态"}},getComStatusClass:e=>{switch(e){case"0":case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},getConfirmStatusText:e=>{switch(e){case"0":return"待确认";case"1":return"已确认";default:return""}},getConfirmStatusClass:e=>{switch(e){case"0":return"status-pending";case"1":return"status-completed";default:return""}},formatDate:e=>{if(!e)return"未知日期";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},getButtonText:e=>"0"===e.confirmStatus&&"1"===e.comStatus?"去签署":"4"===e.comStatus?"重新签署":"查看详情",handleButtonAction:t=>{var a;a=t.id,e.index.navigateTo({url:`/pages/mine/agreements/chan/agreementDetail?id=${a}`})}}}};if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("ZPaging"))()}Math;const r=e._export_sfc(a,[["render",function(t,a,r,n,s,o){return{a:e.p({title:"合同信息",showLeft:!0,path:"/pages/tabbar/tabbar?activeTab=mine"}),b:e.f(n.agreements,((t,a,r)=>e.e$1({a:e.t(t.perAgreementCode),b:e.t(n.formatDate(t.agreementStartDate)),c:e.t(n.formatDate(t.agreementEndDate)),d:t.renewalDate},t.renewalDate?{e:e.t(n.formatDate(t.renewalDate))}:{},{f:e.t(n.getConfirmStatusText(t.confirmStatus)),g:e.n(n.getConfirmStatusClass(t.confirmStatus)),h:e.t(n.getComStatusText(t.comStatus)),i:e.n(n.getComStatusClass(t.comStatus)),j:"0"===t.confirmStatus&&"0"===t.comStatus},"0"===t.confirmStatus&&"0"===t.comStatus?{}:{k:e.t(n.getButtonText(t)),l:e.n("0"!==t.confirmStatus||"1"!==t.comStatus&&"3"!==t.comStatus&&"4"!==t.comStatus?"view-btn":"sign-btn"),m:e.o((e=>n.handleButtonAction(t)),t.id)},{n:t.id}))),c:e.sr("paging","33ef6734-1"),d:e.o(n.queryAgreementList),e:e.o((e=>n.agreements=e)),f:e.p({fixed:!1,height:"calc(100vh - "+n.navHeight+"px)",offset:n.navHeight,"safe-area-inset-top":!1,"show-refresher-when-reload":!0,"refresher-threshold":80,"empty-view-text":"暂无合同信息",modelValue:n.agreements}),g:e.gei(t,"")}}]]);wx.createPage(r);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/mine/agreements/chan/agreementList.js.map
