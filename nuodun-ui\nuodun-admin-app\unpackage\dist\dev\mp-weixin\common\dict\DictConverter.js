"use strict";const t=require("./DictOptions.js"),e=require("./DictData.js");function i(t,...e){return e.find((e=>Object.prototype.hasOwnProperty.call(t,e)))}exports.dictConverter=function(n,r){const o=i(n,r.labelField,...t.options.DEFAULT_LABEL_FIELDS),c=i(n,r.valueField,...t.options.DEFAULT_VALUE_FIELDS);return new e.DictData(n[o],n[c],n)};
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/dict/DictConverter.js.map
