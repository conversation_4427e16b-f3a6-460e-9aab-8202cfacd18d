"use strict";const t=require("../../../common/vendor.js"),e=require("../../../api/home/<USER>"),a=require("../../../config/environment.js"),r=require("../../../common/utils.js");if(!Array){t.resolveComponent("up-button")()}Math;const s={__name:"family",setup(s,{expose:n}){t.useStore(),a.environment.fileUrl;const i=t.ref([]),u=t.ref({}),o=t.computed((()=>{const t=new Date;return`${t.getFullYear()}年${t.getMonth()+1}月${t.getDate()}日`})),c=async()=>{try{const a=await e.getStInfoByFam();a.data&&a.data.length>0&&(i.value=a.data),u.value=t.index.getStorageSync("userInfo")||{}}catch(a){t.index.__f__("error","at pages/tabbar/home/<USER>","获取学生列表失败",a)}},l=t=>t?t.split(" ")[0].replace(/-/g,"/"):"暂无日期",m=t=>{switch(t){case"1":return"待定校确认";case"2":return"已确认定校";case"3":return"已退回定校";case"4":return"定校被驳回";default:return"未知状态"}},d=t=>{switch(t){case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},g=t=>{switch(t){case"1":return"待签名确认";case"2":return"已签名确认";case"3":return"签名已退回";case"4":return"协议被驳回";default:return"未知状态"}},f=t=>{switch(t){case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}};return t.onMounted((()=>{c()})),n({refresh:async()=>{try{return await c(),!0}catch(e){return t.index.__f__("error","at pages/tabbar/home/<USER>","刷新家长首页数据失败",e),!1}},maskPhone:r.maskPhone}),(e,a)=>t.e$1({a:t.t(u.value.nickName||"家长"),b:t.t(o.value),c:i.value&&i.value.length>0},i.value&&i.value.length>0?{d:t.f(i.value,((e,a,s)=>t.e$1({a:t.t(e.stName||"未获取到孩子信息"),b:t.t(t.unref(r.maskPhone)(e.stPhone)||"-"),c:t.o((a=>{return r=e.stId,void t.index.navigateTo({url:`/pages/home/<USER>/detail?stId=${r}`});var r}),e.stId),d:"9ea75fa1-0-"+s,e:e.agreementList&&e.agreementList.length>0},e.agreementList&&e.agreementList.length>0?{f:t.f(e.agreementList,((a,r,s)=>t.e$1({a:"0"!==a.agreeStatus},"0"!==a.agreeStatus?t.e$1({b:t.t(a.stuAgreementCode||"暂无"),c:t.t(l(a.createTime)),d:"1"==a.agreeStatus||"4"==a.agreeStatus},"1"==a.agreeStatus||"4"==a.agreeStatus?{e:t.t("1"==a.agreeStatus?"去签名确认":"查看驳回原因"),f:t.n(f(a.agreeStatus))}:{g:t.t(g(a.agreeStatus)),h:t.n(f(a.agreeStatus))},{i:t.o((r=>((e,a)=>{t.index.navigateTo({url:`/pages/mine/agreements/stInfo/contract/agreementDetail?id=${e.id}&stId=${a}`})})(a,e.stId)),r)}):{},{j:a.stContractType&&a.stContractType.startsWith("S-")&&"0"!==a.confirmationStatus},a.stContractType&&a.stContractType.startsWith("S-")&&"0"!==a.confirmationStatus?t.e$1({k:t.t(a.confirmationCode||"暂无"),l:t.t(l(a.createTime)),m:"1"==a.confirmationStatus||"4"==a.confirmationStatus},"1"==a.confirmationStatus||"4"==a.confirmationStatus?{n:t.t("1"==a.confirmationStatus?"去定校确认":"查看驳回原因"),o:t.n(d(a.confirmationStatus))}:{p:t.t(m(a.confirmationStatus)),q:t.n(d(a.confirmationStatus))},{r:t.o((r=>((e,a)=>{t.index.navigateTo({url:`/pages/mine/agreements/stInfo/school/agreementDetail?id=${e.id}&stId=${a}`})})(a,e.stId)),r)}):{},{s:a.stContractType&&a.stContractType.startsWith("S-")&&e.stMaterial&&"0"!==e.stMaterial.stFileStatus},a.stContractType&&a.stContractType.startsWith("S-")&&e.stMaterial&&"0"!==e.stMaterial.stFileStatus?t.e$1({t:"1"===e.stMaterial.stFileStatus},(e.stMaterial.stFileStatus,{}),{v:"2"===e.stMaterial.stFileStatus},(e.stMaterial.stFileStatus,{}),{w:"1"===e.stMaterial.stFileStatus&&e.stMaterial.stFileRemark},"1"===e.stMaterial.stFileStatus&&e.stMaterial.stFileRemark?{x:t.t(e.stMaterial.stFileRemark)}:{},{y:t.o((a=>{return r=e.stId,void t.index.navigateTo({url:`/pages/home/<USER>/materialUpload?stId=${r}`});var r}),r)}):{},{z:r})))}:{},{g:e.stId})))}:{},{e:t.gei(e,"")})}},n=t._export_sfc(s,[["__scopeId","data-v-9ea75fa1"]]);wx.createComponent(n);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/tabbar/home/<USER>
