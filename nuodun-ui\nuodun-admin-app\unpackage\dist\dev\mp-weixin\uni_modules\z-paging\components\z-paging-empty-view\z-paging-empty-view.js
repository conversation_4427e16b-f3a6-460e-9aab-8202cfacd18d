"use strict";const e=require("../z-paging/js/z-paging-static.js"),t=require("../../../../common/vendor.js"),i={name:"z-paging-empty-view",data:()=>({}),props:{emptyViewText:{type:String,default:"没有数据哦~"},emptyViewImg:{type:String,default:""},showEmptyViewReload:{type:Boolean,default:!1},emptyViewReloadText:{type:String,default:"重新加载"},isLoadFailed:{type:Boolean,default:!1},emptyViewStyle:{type:Object,default:function(){return{}}},emptyViewImgStyle:{type:Object,default:function(){return{}}},emptyViewTitleStyle:{type:Object,default:function(){return{}}},emptyViewReloadStyle:{type:Object,default:function(){return{}}},emptyViewZIndex:{type:Number,default:9},emptyViewFixed:{type:<PERSON>olean,default:!0},unit:{type:String,default:"rpx"}},computed:{emptyImg(){return this.isLoadFailed?e.zStatic.base64Error:e.zStatic.base64Empty},finalEmptyViewStyle(){return this.emptyViewStyle["z-index"]=this.emptyViewZIndex,this.emptyViewStyle}},methods:{reloadClick(){this.$emit("reload")},emptyViewClick(){this.$emit("viewClick")}}};const p=t._export_sfc(i,[["render",function(e,i,p,y,l,m){return t.e$1({a:!p.emptyViewImg.length},p.emptyViewImg.length?{f:"rpx"===p.unit?1:"",g:"px"===p.unit?1:"",h:t.s(p.emptyViewImgStyle),i:p.emptyViewImg}:{b:"rpx"===p.unit?1:"",c:"px"===p.unit?1:"",d:t.s(p.emptyViewImgStyle),e:m.emptyImg},{j:t.t(p.emptyViewText),k:"rpx"===p.unit?1:"",l:"px"===p.unit?1:"",m:t.s(p.emptyViewTitleStyle),n:p.showEmptyViewReload},p.showEmptyViewReload?{o:t.t(p.emptyViewReloadText),p:"rpx"===p.unit?1:"",q:"px"===p.unit?1:"",r:t.s(p.emptyViewReloadStyle),s:t.o(((...e)=>m.reloadClick&&m.reloadClick(...e)))}:{},{t:p.emptyViewFixed?1:"",v:t.s(m.finalEmptyViewStyle),w:t.o(((...e)=>m.emptyViewClick&&m.emptyViewClick(...e))),x:t.gei(e,"")})}],["__scopeId","data-v-b7999e14"]]);wx.createComponent(p);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging-empty-view/z-paging-empty-view.js.map
