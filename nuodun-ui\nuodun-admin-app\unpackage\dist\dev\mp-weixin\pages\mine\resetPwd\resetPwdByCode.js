"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/open.js"),s=require("../../../api/user.js"),a=require("../../../common/validate.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../../components/custom-nav/custom-nav.js")+(()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const r={__name:"resetPwdByCode",setup(r){const{proxy:n}=e.getCurrentInstance(),t=n.toast,u=e.useStore(),l=e.ref(!0),v=e.ref(!0),c=e.ref(0);let i=null;const d=e.ref(""),p=e.reactive({verCode:"",newPassword:"",confirmPassword:"",verType:"app_resetps"}),h=e.computed((()=>d.value?11===d.value.length?d.value.slice(0,3)+"****"+d.value.slice(7):d.value:"加载中...")),w=()=>{c.value>0||(d.value?o.sendVerCode({phone:d.value,verType:"app_resetps"}).then((()=>{t.show("验证码已发送"),m()})).catch((o=>{e.index.__f__("error","at pages/mine/resetPwd/resetPwdByCode.vue:111",o),t.show("验证码发送失败")})):t.show("未获取到手机号，请返回重试"))},m=()=>{c.value=60,i=setInterval((()=>{c.value--,c.value<=0&&clearInterval(i)}),1e3)},f=()=>{d.value?p.verCode?a.checkPwd(p.newPassword)&&(p.confirmPassword==p.newPassword?s.resetUserPwdByCode(d.value,p.verCode,p.newPassword,p.verType).then((o=>{t.show("修改成功"),setTimeout((function(){e.index.redirectTo({url:"/pages/tabbar/tabbar"})}),300)})).catch((o=>{o&&o.msg&&o.msg.includes("手机号已修改")?(t.show("手机号已修改，请重新登录"),setTimeout((()=>{u.dispatch("LogOut").then((()=>{e.index.reLaunch({url:"/pages/login/login"})}))}),1e3)):t.show((null==o?void 0:o.msg)||"修改失败")})):t.show("两次输入的密码不一致")):t.show("请填写验证码"):t.show("未获取到手机号，请返回重试")};return e.onLoad((()=>{(()=>{const e=u.state.userInfo;e&&e.phonenumber?d.value=e.phonenumber:u.dispatch("GetInfo",{isJump:!1}).then((()=>{const e=u.state.userInfo;e&&e.phonenumber?d.value=e.phonenumber:t.show("获取手机号失败，请返回重试")})).catch((()=>{t.show("获取用户信息失败")}))})()})),e.onUnload((()=>{i&&clearInterval(i)})),(o,s)=>({a:e.p({title:"修改密码",showLeft:!0,path:"/pages/tabbar/tabbar?activeTab=mine"}),b:e.t(h.value),c:p.verCode,d:e.o((e=>p.verCode=e.detail.value)),e:e.t(c.value>0?`${c.value}秒后重试`:"获取验证码"),f:e.o(w),g:c.value>0?1:"",h:l.value,i:p.newPassword,j:e.o((e=>p.newPassword=e.detail.value)),k:e.p({type:l.value?"eye-slash":"eye",size:"20",color:"#666"}),l:e.o((e=>l.value=!l.value)),m:v.value,n:p.confirmPassword,o:e.o((e=>p.confirmPassword=e.detail.value)),p:e.p({type:v.value?"eye-slash":"eye",size:"20",color:"#666"}),q:e.o((e=>v.value=!v.value)),r:e.o(f),s:e.gei(o,"")})}},n=e._export_sfc(r,[["__scopeId","data-v-9ffda5e0"]]);wx.createPage(n);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mine/resetPwd/resetPwdByCode.js.map
