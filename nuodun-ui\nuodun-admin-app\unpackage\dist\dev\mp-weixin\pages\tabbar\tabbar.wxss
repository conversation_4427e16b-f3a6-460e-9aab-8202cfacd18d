:root{--nuodun-primary-color: #409EFF}.tabbar-container{height:100vh;display:flex;flex-direction:column;position:relative;overflow:hidden;background-color:#f8f8f8}.content-container{flex:1;background-color:#f8f8f8;-webkit-overflow-scrolling:touch}.content-container.hidden-scrollbar{overflow:hidden}.page-content{padding-bottom:var(--tabbar-height, 100rpx);box-sizing:border-box;min-height:101%}.carousel-wrapper{width:100%;height:320rpx;margin-bottom:20rpx;border-radius:8rpx;overflow:hidden}.carousel,.carousel-image{width:100%;height:100%}.carousel-title{position:absolute;bottom:0;left:0;right:0;background-color:rgba(0,0,0,.4);color:#fff;padding:10rpx 20rpx;font-size:28rpx;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.custom-tabbar{position:fixed;bottom:0;left:0;right:0;height:100rpx;background:#fff;display:flex;box-shadow:0 -2rpx 10rpx rgba(0,0,0,.05);z-index:999}.safe-area-bottom{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tabbar-item{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:4rpx;height:100rpx}.tabbar-text{font-size:24rpx;color:#7a7e83;transition:color .3s}.tabbar-text.active{color:var(--nuodun-primary-color)}
