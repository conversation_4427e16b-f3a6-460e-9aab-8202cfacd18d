"use strict";const e=require("../../../../../common/vendor.js"),a=require("../../../../../api/home/<USER>"),t=require("../../../../../config/environment.js");if(!Array){(e.resolveComponent("up-icon")+e.resolveComponent("uni-popup"))()}const n=()=>"../../../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";Math||((()=>"../../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+e.unref(o)+i+u+n)();const o=()=>"../../../../../components/custom-nav/custom-nav.js",i=()=>"../../../../../components/document-preview/index.js",u=()=>"../../../../../components/school-application-info/index.js",r={__name:"agreementDetail",setup(n){const{proxy:o}=e.getCurrentInstance(),i=o.toast,u=t.environment.fileUrl||"",r=e.ref(null),l=e.ref(null),s=e.ref([]),c=e.ref(""),v=e.ref(""),m=e.ref(""),f=e.ref(""),d=e.computed((()=>!!l.value&&("1"===l.value.confirmationStatus||"4"===l.value.confirmationStatus))),p=e.computed((()=>s.value&&s.value.length>0?s.value:[])),g=async t=>{try{const o=await a.getContractAgreement(t);if(l.value=o.data,l.value&&l.value.confirmationJson)try{const e=JSON.parse(l.value.confirmationJson);s.value=e.planList}catch(n){e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementDetail.vue:138","解析确认JSON失败",n)}}catch(n){e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementDetail.vue:142","获取协议详情失败",n),i.show("获取协议详情失败")}},h=e=>{switch(e){case"0":return"未确认";case"1":return"待确认";case"2":return"已确认定校";case"3":return"已退回定校";case"4":return"协议被驳回";default:return"未知状态"}},S=e=>{switch(e){case"0":case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected"}},w=()=>{l.value&&l.value.id?e.index.navigateTo({url:`/pages/mine/agreements/stInfo/school/agreementSign?id=${l.value.id}&agreementCode=${encodeURIComponent(l.value.stuAgreementCode||"")}`}):i.show("协议信息不完整")},_=()=>{l.value&&l.value.orderId?e.index.navigateTo({url:`/pages/mine/agreements/stInfo/school/agreementHistory?orderId=${l.value.orderId}`}):i.show("协议信息不完整")},j=()=>{c.value="退回确认",v.value="确定要退回该协议吗？",m.value="",f.value="reject",r.value.open()},R=()=>{r.value.close()},x=()=>{if("reject"===f.value){if(!m.value.trim())return void i.show("请输入退回原因");C("3",m.value)}r.value.close()},C=async(t,n)=>{if(l.value&&l.value.id){e.index.showLoading("处理中...");try{await a.confirmSignConfirmation({id:l.value.id,confirmationConfirmStatus:t,confirmationRejectRemark:n});i.show("退回成功"),setTimeout((()=>{g(l.value.id)}),500)}catch(o){e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementDetail.vue:264","更新协议状态失败:",o)}finally{e.index.hideLoading()}}else i.show("协议信息不完整")};return e.onLoad((a=>{a.id?g(a.id):(i.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1500))})),e.onShow((()=>{l.value&&l.value.id&&g(l.value.id)})),(a,t)=>{return e.e$1({a:e.p({name:"order",size:"23"}),b:e.o(_),c:e.p({title:"定校协议",isBack:!0,showLeft:!0,path:"/",showRight:!0}),d:l.value},l.value?e.e$1({e:e.t(l.value.stuAgreementCode||"暂无"),f:e.t((n=l.value.createTime,(n?n.split(" ")[0].replace(/-/g,"/"):"暂无日期")||"暂无")),g:e.t(h(l.value.confirmationStatus)),h:e.n(S(l.value.confirmationStatus)),i:"3"===l.value.confirmationStatus&&l.value.confirmationRejectRemark},"3"===l.value.confirmationStatus&&l.value.confirmationRejectRemark?{j:e.t(l.value.confirmationRejectRemark)}:{},{k:"4"===l.value.confirmationStatus&&l.value.confirmationRebutRemark},"4"===l.value.confirmationStatus&&l.value.confirmationRebutRemark?{l:e.t(l.value.confirmationRebutRemark)}:{}):{},{m:l.value&&l.value.confirmationFilePath},l.value&&l.value.confirmationFilePath?e.e$1({n:l.value.confirmationFilePath},l.value.confirmationFilePath?{o:e.p({"file-path":e.unref(u)+l.value.confirmationFilePath,"file-name":"定校协议","allow-download":!0})}:{}):{},{p:e.p({schoolList:p.value}),q:d.value},(d.value,{}),{r:d.value},d.value?e.e$1({s:"1"===l.value.confirmationStatus||"4"===l.value.confirmationStatus},"1"===l.value.confirmationStatus||"4"===l.value.confirmationStatus?{t:e.o(j)}:{},{v:"1"===l.value.confirmationStatus||"4"===l.value.confirmationStatus},"1"===l.value.confirmationStatus||"4"===l.value.confirmationStatus?{w:e.o(w)}:{}):{},{x:e.t(c.value),y:e.t(v.value),z:m.value,A:e.o((e=>m.value=e.detail.value)),B:e.t(m.value.length),C:e.o(R),D:e.o(x),E:e.sr(r,"fd7a251e-4",{k:"customDialog"}),F:e.p({type:"center"}),G:e.gei(a,"")});var n}}},l=e._export_sfc(r,[["__scopeId","data-v-fd7a251e"]]);wx.createPage(l);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/school/agreementDetail.js.map
