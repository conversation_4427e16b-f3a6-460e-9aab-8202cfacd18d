<template>
    <view class="container">
        <custom-nav :title="'文件预览'" :showLeft="true" />

        <view class="preview-content">
            <file-office v-if="showFileOffice" :file-path="filePath" :file-type="fileType" class="office-preview"
                @rendered="onFileRendered" @error="onFileError">
            </file-office>

            <file-view v-else-if="showFileView" :file-path="filePath" :file-name="fileName" class="file-view">
            </file-view>

            <view v-else class="no-preview">
                <text>无法预览该文件类型</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import FileOffice from '@/components/file-office/index.vue'
import FileView from '@/components/file-view/index.vue'
const { proxy } = getCurrentInstance();
const toast = proxy.toast;

// 响应式数据
const filePath = ref('')
const fileName = ref('')
const showFileOffice = ref(false)
const showFileView = ref(false)
const fileType = ref('')

// 初始化文件预览
const initFilePreview = () => {
    if (!filePath.value) {
        toast.show('没有可预览的文件')
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)
        return
    }

    const fileExt = filePath.value.split('.').pop()?.toLowerCase() || ''

    // #ifdef H5
    // H5环境使用完整的预览功能
    if (fileExt === 'pdf' || fileExt === 'doc' || fileExt === 'docx' ||
        fileExt === 'xls' || fileExt === 'xlsx' || fileExt === 'csv' ||
        ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) {
        showFileOffice.value = true
        showFileView.value = false

        // 设置文件类型
        if (fileExt === 'pdf') fileType.value = 'PDF'
        else if (['doc', 'docx', 'rtf'].includes(fileExt)) fileType.value = 'WORD'
        else if (['xls', 'xlsx', 'csv'].includes(fileExt)) fileType.value = 'EXCEL'
        else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) fileType.value = 'IMAGE'
    } else {
        // 其他文件使用FileView组件
        showFileView.value = true
        showFileOffice.value = false
    }
    // #endif

    // #ifndef H5
    // 非H5环境（小程序、APP）优先使用原生预览
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExt)) {
        // 图片文件直接使用uni.previewImage
        uni.previewImage({
            urls: [filePath.value],
            current: filePath.value,
            fail: (err) => {
                console.error('图片预览失败:', err)
                toast.show('图片预览失败')
                // 图片预览失败时使用组件预览
                showFileOffice.value = true
                fileType.value = 'IMAGE'
            }
        })
        return
    } else if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExt)) {
        // 文档文件使用原生预览
        previewDocumentNative()
        return
    } else {
        // 其他文件类型使用组件预览
        showFileView.value = true
        showFileOffice.value = false
    }
    // #endif
}

// 原生文档预览（小程序、APP）
const previewDocumentNative = () => {
    uni.showLoading({
        title: '准备预览...',
        mask: true
    })

    uni.downloadFile({
        url: filePath.value,
        success: (res) => {
            if (res.statusCode === 200) {
                uni.openDocument({
                    filePath: res.tempFilePath,
                    showMenu: true,
                    success: () => {
                        uni.hideLoading()
                        console.log('文档预览成功')
                        // #ifdef MP-WEIXIN
                        // 微信小程序提示用户可以保存
                        setTimeout(() => {
                            uni.showToast({
                                title: '可点击右上角保存到手机',
                                icon: 'none',
                                duration: 3000
                            })
                        }, 1000)
                        // #endif

                        // #ifndef MP-WEIXIN
                        // 其他环境预览成功后返回上一页
                        setTimeout(() => {
                            uni.navigateBack()
                        }, 500)
                        // #endif
                    },
                    fail: (err) => {
                        uni.hideLoading()
                        console.error('文档预览失败:', err)
                        toast.show('原生预览失败，使用组件预览')
                        // 原生预览失败时使用组件预览
                        fallbackToComponentPreview()
                    }
                })
            } else {
                uni.hideLoading()
                toast.show('文件下载失败')
                uni.navigateBack()
            }
        },
        fail: (err) => {
            uni.hideLoading()
            console.error('文件下载失败:', err)
            toast.show('文件下载失败')
            uni.navigateBack()
        }
    })
}

// 回退到组件预览
const fallbackToComponentPreview = () => {
    const fileExt = filePath.value.split('.').pop()?.toLowerCase() || ''

    showFileOffice.value = true
    showFileView.value = false

    // 设置文件类型
    if (fileExt === 'pdf') fileType.value = 'PDF'
    else if (['doc', 'docx', 'rtf'].includes(fileExt)) fileType.value = 'WORD'
    else if (['xls', 'xlsx', 'csv'].includes(fileExt)) fileType.value = 'EXCEL'
    else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) fileType.value = 'IMAGE'
}

// 文件渲染完成回调
const onFileRendered = () => {
    // 文件渲染完成的处理逻辑
}

// 文件渲染错误回调
const onFileError = (error) => {
    console.error('文件预览失败:', error)
    toast.show('文件预览失败')
}

// 页面加载
onLoad((options) => {
    if (options.filePath) {
        filePath.value = decodeURIComponent(options.filePath)
        initFilePreview()
    }

    if (options.fileName) {
        fileName.value = decodeURIComponent(options.fileName)
    }
})
</script>

<style lang="scss">
.container {
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.preview-content {
    flex: 1;
    position: relative;
    width: 100%;
    overflow: hidden;
    height: calc(100vh - 85px);
}

.office-preview,
.file-view {
    width: 100%;
    height: 100%;
}

.no-preview {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    text {
        font-size: 32rpx;
        color: #999;
    }
}
</style>