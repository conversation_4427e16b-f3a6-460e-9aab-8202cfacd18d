"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),r=require("../../config/environment.js"),n=require("../../common/auth.js"),a=require("../../api/open.js");if(!Array){e.resolveComponent("uni-icons")()}Math;const i={__name:"login",setup(i){const{proxy:s}=e.getCurrentInstance(),t=s.toast,d=e.useStore(),c=e.ref(!0),p=e.reactive({username:"",password:"",rememberMe:!1,agreedToTerms:!1,code:"",uuid:"",loginType:"PASSWORD",platType:"USER",appId:r.environment.currentAppId}),u=e.computed((()=>!0));e.onMounted((()=>{const e=n.getAndDecrypt("savedCredentials");if(e){const{username:o,password:r,rememberMe:n}=e;p.username=o,p.password=r,p.rememberMe=n}else p.rememberMe=!1;c.value=!0}));const m=()=>{p.rememberMe=!p.rememberMe},l=()=>{c.value=!c.value},g=()=>{const e=document.querySelector('input[type="text"][password]');e&&e.focus()},w=()=>{p.agreedToTerms=!p.agreedToTerms},v=async()=>{if(p.code="",p.username)if(p.password)if(p.agreedToTerms)try{await d.dispatch("Login",{userInfo:p}),p.rememberMe?n.encryptAndStore("savedCredentials",{username:p.username,password:p.password,rememberMe:!0}):e.index.removeStorageSync("savedCredentials"),await d.dispatch("GetInfo",{isJump:!0})}catch(o){e.index.__f__("error","at pages/login/login.vue:203",o)}else t.show("请阅读并同意服务协议和隐私政策");else t.show("请填写密码");else t.show("请填写账号")},h=e=>{p.loginType=e},y=async e=>await a.determineWxUser({code:e,appId:r.environment.currentAppId}),_=()=>!!p.agreedToTerms||(t.show("请阅读并同意服务协议和隐私政策"),!1),f=async o=>{if(e.index.__f__("log","at pages/login/login.vue:294","handleMiniProgramLogin",o),_())if(o.detail.code)try{const n=await new Promise(((o,r)=>{e.index.login({provider:"weixin",success:o,fail:r})}));if(!n.code)return void t.show("获取登录信息失败");if("Y"===(await y(n.code)).bindStatus)return t.show("登录中..."),void(await(async o=>{if("Y"!==(await y(o)).bindStatus)return t.show("请先绑定！"),void e.index.navigateTo({url:"/pages/login/bindPhone?code="+o});p.code=o;try{await d.dispatch("Login",{userInfo:p}),await d.dispatch("GetInfo",{isJump:!0}),e.index.reLaunch({url:"/"})}catch(r){t.show("微信登录失败")}})(n.code));t.show("绑定中...");const i=await a.bindWxMiniProgram({phoneCode:o.detail.code,loginCode:n.code,appId:r.environment.wxAppId});"Y"===i.bindStatus?(t.show("绑定成功，登录中..."),p.code=n.code,await d.dispatch("Login",{userInfo:p}),await d.dispatch("GetInfo",{isJump:!0}),e.index.reLaunch({url:"/"})):t.show(i.message||"绑定失败，请联系管理员")}catch(n){e.index.__f__("error","at pages/login/login.vue:353","一键登录失败:",n),t.show("登录失败，请重试")}else t.show("获取手机号失败，请重试")},T=o=>{e.index.navigateTo({url:`/pages/transition/webView?url=${encodeURIComponent({service:"http://www.ndjykj.com/agreement/nuodun-service.html",privacy:"http://www.ndjykj.com/agreement/nuodun-policy.html"}[o])}&title=${{service:"服务协议",privacy:"隐私政策"}[o]}`})};return(r,n)=>e.e$1({a:o._imports_0,b:u.value},u.value?{c:e.n("WX_OPEN"===p.loginType?"active":""),d:e.o((e=>h("WX_OPEN")))}:{},{e:e.n("PASSWORD"===p.loginType?"active":""),f:e.o((e=>h("PASSWORD"))),g:"PASSWORD"===p.loginType},"PASSWORD"===p.loginType?{h:e.o(g),i:p.username,j:e.o((e=>p.username=e.detail.value)),k:c.value,l:e.o(v),m:p.password,n:e.o((e=>p.password=e.detail.value)),o:e.p({type:c.value?"eye-slash":"eye",size:"20",color:"#666"}),p:e.o(l),q:p.rememberMe,r:e.o(m)}:{s:o._imports_1},{t:"PASSWORD"===p.loginType},"PASSWORD"===p.loginType?{v:e.o(v)}:{},{w:"WX_OPEN"===p.loginType},"WX_OPEN"===p.loginType?{x:e.o(f),y:e.o(_)}:{},{z:p.agreedToTerms,A:e.o(w),B:e.o((e=>T("service"))),C:e.o((e=>T("privacy"))),D:e.gei(r,"")})}},s=e._export_sfc(i,[["__scopeId","data-v-e4e4508d"]]);wx.createPage(s);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
