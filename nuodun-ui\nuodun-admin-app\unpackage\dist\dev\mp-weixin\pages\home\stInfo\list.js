"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/home/<USER>");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("dict-tag"))()}Math||((()=>"../../../components/custom-nav/custom-nav.js")+a+s)();const s=()=>"../../../uni_modules/z-paging/components/z-paging/z-paging.js",a=()=>"../../../components/songlazy-sl-filter/sl-filter/sl-filter.js",o={__name:"list",setup(s){const{proxy:a}=e.getCurrentInstance();a.toast;const{t_st_deca_type:o}=a.useDict("t_st_deca_type"),l=e.ref(null),n=e.ref(0),r=e.ref([]),i=e.ref(null),c=e.computed((()=>[{title:"学生类型",key:"stDecaType",isSort:!1,isMutiple:!0,reflexTitle:!1,detailList:[{title:"不限",value:"",isSelected:!0},...o.value.map((e=>({title:e.label,value:e.value,isSelected:!1})))]}])),u=e.reactive({orderByColumn:"si.st_id",stName:"",stDecaType:""});e.onMounted((()=>{const t=e.index.getSystemInfoSync();n.value=t.statusBarHeight+95}));const p=e=>{u.stName=e.stName||"",u.stDecaType=e.stDecaType||"",m()},d=(s,a)=>{const o={...u,pageNum:s,pageSize:a};t.getStInfoList(o).then((e=>{200===e.code?l.value.complete(e.rows):l.value.complete(!1)})).catch((t=>{e.index.__f__("error","at pages/home/<USER>/list.vue:115",t),l.value.complete(!1)}))},m=()=>{l.value&&l.value.reload()};return(t,s)=>({a:e.p({title:"学生列表",showLeft:!0,path:"/"}),b:e.sr(i,"4e2fecb3-1",{k:"slFilter"}),c:e.o(p),d:e.p({menuList:c.value,themeColor:"var(--nuodun-primary-color)",topFixed:!0}),e:e.f(r.value,((t,s,a)=>({a:e.t(t.stName),b:"4e2fecb3-3-"+a+",4e2fecb3-2",c:e.p({options:e.unref(o),value:t.stDecaType,showTag:!0}),d:e.o((s=>(t=>{e.index.navigateTo({url:`/pages/home/<USER>/index?stId=${t.stId}`})})(t)),s),e:e.t(t.createTime),f:s,g:e.o((s=>(t=>{e.index.navigateTo({url:`/pages/home/<USER>/detail?stId=${t.stId}`})})(t)),s)}))),f:e.sr(l,"4e2fecb3-2",{k:"paging"}),g:e.o(d),h:e.o((e=>r.value=e)),i:e.p({fixed:!1,height:"calc(100vh - "+n.value+"px)",offset:n.value,"safe-area-inset-top":!1,"show-refresher-when-reload":!0,"refresher-threshold":80,"empty-view-text":"暂无学生记录",modelValue:r.value}),j:e.gei(t,"")})}},l=e._export_sfc(o,[["__scopeId","data-v-4e2fecb3"]]);wx.createPage(l);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/list.js.map
