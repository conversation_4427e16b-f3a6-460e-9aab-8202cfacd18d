"use strict";const r=require("../../common/vendor.js"),e={__name:"index",props:{schoolList:{type:Array,default:()=>[]}},setup(e){const o=e,s=r.reactive({}),t=r.computed((()=>{if(!o.schoolList||0===o.schoolList.length)return[];const r=[...o.schoolList].sort(((r,e)=>(r.schoolOrder||999)-(e.schoolOrder||999))),e={};return r.forEach((r=>{const o=r.schoolOrder||"其他";e[o]||(e[o]=[]),e[o].push(r)})),Object.keys(e).forEach((r=>{e[r]=e[r].sort(((r,e)=>(r.majorOrder||999)-(e.majorOrder||999)))})),Object.keys(e).sort(((r,e)=>(isNaN(r)?999:Number(r))-(isNaN(e)?999:Number(e)))).map((r=>({schoolName:e[r][0].applySchool,order:r,label:a(r),schools:e[r]})))})),a=r=>{if(isNaN(r))return"其他志愿";const e=Number(r),o=["一","二","三","四","五","六","七","八","九","十"];if(e>=1&&e<=10)return"第"+o[e-1]+"志愿";if(e>10){const r=Math.floor(e/10),s=e%10;let t="";return t=1===r?"十":o[r-1]+"十",s>0&&(t+=o[s-1]),"第"+t+"志愿"}return"其他志愿"},c=r=>(r.majorOrder?`${r.majorOrder}. `:"")+(r.applyMajorZh||"");return(e,o)=>r.e$1({a:t.value&&t.value.length>0},t.value&&t.value.length>0?{b:r.f(t.value,((e,o,t)=>({a:r.t(e.label),b:r.t(e.schoolName),c:r.f(e.schools,((e,t,a)=>{return r.e$1({a:!s[o+"-"+t]},s[o+"-"+t]?{}:{b:r.t(c(e))},{c:r.t(s[o+"-"+t]?"▲":"▼"),d:r.o((r=>{var e;s[e=o+"-"+t]=!s[e]}),"school-"+t),e:s[o+"-"+t]},s[o+"-"+t]?r.e$1({f:e.applyMajorZh},e.applyMajorZh?{g:r.t((e.majorOrder?e.majorOrder+". ":"")+e.applyMajorZh)}:{},{h:e.admissionRequirements},e.admissionRequirements?{i:(l=e.admissionRequirements,l||"")}:{}):{},{j:"school-"+t});var l})),d:"group-"+o})))}:{},{c:r.gei(e,"")})}},o=r._export_sfc(e,[["__scopeId","data-v-f1394213"]]);wx.createComponent(o);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/school-application-info/index.js.map
