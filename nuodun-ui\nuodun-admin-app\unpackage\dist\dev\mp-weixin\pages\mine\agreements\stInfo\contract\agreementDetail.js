"use strict";const e=require("../../../../../common/vendor.js"),a=require("../../../../../api/home/<USER>"),t=require("../../../../../config/environment.js");if(!Array){e.resolveComponent("uni-popup")()}const u=()=>"../../../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";Math||(e.unref(r)+n+u)();const r=()=>"../../../../../components/custom-nav/custom-nav.js",n=()=>"../../../../../components/document-preview/index.js",s={__name:"agreementDetail",setup(u){const{proxy:r}=e.getCurrentInstance(),n=r.toast,s=t.environment.fileUrl||"",l=e.ref(null),o=e.ref(null),v=e.ref(null),i=e.ref(null),c=e.ref(null),m=e.ref(""),d=e.ref(""),g=e.ref(""),f=e.ref(""),p=e.computed((()=>!!o.value&&("1"===o.value.agreeStatus||"4"===o.value.agreeStatus))),S=async t=>{try{const r=await a.getContractAndAgreement(t);if(r.data.agreement&&(o.value=r.data.agreement,o.value&&o.value.agreementJson))try{i.value=JSON.parse(o.value.agreementJson)}catch(u){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementDetail.vue:194","解析协议JSON失败",u)}r.data.contract&&(v.value=r.data.contract),r.data.installments&&(c.value=r.data.installments)}catch(u){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementDetail.vue:205","获取协议详情失败",u),n.show("获取协议详情失败")}},h=e=>{switch(e){case"0":return"未确认";case"1":return"待签名确认";case"2":return"已签名确认";case"3":return"签名已退回";case"4":return"协议被驳回";default:return"未知状态"}},w=e=>{switch(e){case"0":case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},D=(e,a,t,u,r)=>{if(r&&r.dueDate){const e=new Date,a=new Date(r.dueDate);if(e.setHours(0,0,0,0),a.setHours(0,0,0,0),e<a)return"未到付款时间"}return"N"===u?"去上传凭证":"Y"===u&&"S"===t?"查看凭证":"Y"===u&&"R"===t?"凭证验证失败":"Y"===u&&"W"===t?"凭证审核中":""},_=(e,a,t,u,r)=>{if(r&&r.dueDate){const e=new Date,a=new Date(r.dueDate);if(e.setHours(0,0,0,0),a.setHours(0,0,0,0),e<a)return"status-waiting"}return"N"===u?"status-pending":"Y"===u&&"S"===t?"status-success":"Y"===u&&"R"===t?"status-rejected":"Y"===u&&"W"===t?"status-pending":""},y=e=>{if(o.value&&"2"===o.value.agreeStatus&&v.value&&"1"===v.value.confirmStatus){const a=new Date,t=e.dueDate?new Date(e.dueDate):null;return!(t&&a<t)&&("W"===e.orderStatus&&("N"===e.isProofUploaded||"R"===e.proofVerifyStatus))}return!1},A=e=>{if(!e||!e.dueDate)return!1;const a=new Date,t=new Date(e.dueDate);return a.setHours(0,0,0,0),t.setHours(0,0,0,0),a<t},j=()=>{o.value&&o.value.id?e.index.navigateTo({url:`/pages/mine/agreements/stInfo/contract/agreementSign?id=${o.value.id}&agreementCode=${encodeURIComponent(o.value.stuAgreementCode||"")}`}):n.show("协议信息不完整")},R=()=>{m.value="退回确认",d.value="确定要退回该协议吗？",g.value="",f.value="reject",l.value.open()},I=()=>{l.value.close()},P=()=>{if("reject"===f.value){if(!g.value.trim())return void n.show("请输入退回原因");x("3",g.value)}l.value.close()},x=async(t,u)=>{if(o.value&&o.value.id){e.index.showLoading("处理中...");try{await a.confirmSignContract({id:o.value.id,agreeStatus:t,comRejectRemark:u});n.show("退回成功"),setTimeout((()=>{S(o.value.id)}),500)}catch(r){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementDetail.vue:404","更新协议状态失败:",r)}finally{e.index.hideLoading()}}else n.show("协议信息不完整")};return e.onLoad((a=>{a.id?S(a.id):(n.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1500))})),e.onShow((()=>{o.value&&o.value.id&&S(o.value.id)})),(a,t)=>e.e$1({a:e.p({title:"合同协议详情",isBack:!0,showLeft:!0,path:"/"}),b:o.value},o.value?e.e$1({c:e.t(o.value.stuAgreementCode||"暂无"),d:e.t(a.parseTime(o.value.createTime)||"暂无"),e:e.t(h(o.value.agreeStatus)),f:e.n(w(o.value.agreeStatus)),g:"3"===o.value.agreeStatus&&o.value.comRejectRemark},"3"===o.value.agreeStatus&&o.value.comRejectRemark?{h:e.t(o.value.comRejectRemark)}:{},{i:"4"===o.value.agreeStatus&&o.value.rebutRemark},"4"===o.value.agreeStatus&&o.value.rebutRemark?{j:e.t(o.value.rebutRemark)}:{}):{},{k:o.value&&o.value.stuAgreementPath},o.value&&o.value.stuAgreementPath?e.e$1({l:"1"===o.value.contractArchiveType},(o.value.contractArchiveType,{}),{m:"2"===o.value.contractArchiveType},(o.value.contractArchiveType,{}),{n:o.value.stuAgreementPath},o.value.stuAgreementPath?{o:e.p({"file-path":e.unref(s)+o.value.stuAgreementPath,"file-name":"合同协议","allow-download":!0})}:{}):{},{p:v.value},v.value?e.e$1({q:"N"===v.value.isInstallments},(v.value.isInstallments,{}),{r:"Y"===v.value.isInstallments},(v.value.isInstallments,{}),{s:v.value},v.value?e.e$1({t:v.value.contractCode},v.value.contractCode?{v:e.t(v.value.contractCode)}:{},{w:v.value.contractName},v.value.contractName?{x:e.t(v.value.contractName)}:{},{y:e.t(v.value.contractAmount)}):{}):{},{z:v.value&&"Y"===v.value.isInstallments&&c.value&&c.value.length>0},v.value&&"Y"===v.value.isInstallments&&c.value&&c.value.length>0?{A:e.f(c.value,((t,u,r)=>e.e$1({a:e.t(u+1)},"1"===o.value.confirmStatus?{b:e.t(D(o.value.confirmStatus,t.orderStatus,t.proofVerifyStatus,t.isProofUploaded,t)),c:e.n(_(o.value.confirmStatus,t.orderStatus,t.proofVerifyStatus,t.isProofUploaded,t))}:{},{d:e.t(t.dueAmount||0),e:e.t(a.parseTime(t.dueDate,"{y}-{m}-{d}")||"暂无"),f:"Y"===t.isProofUploaded},"Y"===t.isProofUploaded?{g:e.t(a.parseTime(t.proofUploadTime)||"暂无")}:{},{h:y(t)},(y(t)||"Y"===t.isProofUploaded&&"1"===v.value.confirmStatus||A(t),{}),{i:"Y"===t.isProofUploaded&&"1"===v.value.confirmStatus,j:A(t),k:u,l:e.o((a=>{var u;(u=t.id)?e.index.navigateTo({url:`/pages/mine/agreements/stInfo/contract/installmentProof?id=${u}`}):n.show("分期信息不完整")}),u)}))),B:"1"===o.value.confirmStatus}:{},{C:p.value},(p.value,{}),{D:p.value},p.value?e.e$1({E:"1"===o.value.agreeStatus||"4"===o.value.agreeStatus},"1"===o.value.agreeStatus||"4"===o.value.agreeStatus?{F:e.o(R)}:{},{G:"1"===o.value.agreeStatus||"4"===o.value.agreeStatus},"1"===o.value.agreeStatus||"4"===o.value.agreeStatus?{H:e.o(j)}:{}):{},{I:e.t(m.value),J:e.t(d.value),K:g.value,L:e.o((e=>g.value=e.detail.value)),M:e.t(g.value.length),N:e.o(I),O:e.o(P),P:e.sr(l,"98781687-2",{k:"customDialog"}),Q:e.p({type:"center"}),R:e.gei(a,"")})}},l=e._export_sfc(s,[["__scopeId","data-v-98781687"]]);wx.createPage(l);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/contract/agreementDetail.js.map
