"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),n=require("../../api/open.js"),r=require("../../config/environment.js"),t=require("../../common/validate.js");if(!Array){e.resolveComponent("custom-nav")()}Math;const a={__name:"bindPhone",setup(a){const{proxy:i}=e.getCurrentInstance(),s=i.toast,c=e.useStore(),d=e.reactive({phone:"",agreedToTerms:!1,code:"",appId:r.environment.currentAppId,verType:"wx_web_bind",verCode:"",type:"te",platType:"USER"}),p=e.reactive({username:"",password:"",code:"",uuid:"",loginType:"WX_OPEN",platType:"USER",appId:r.environment.currentAppId}),v=e.ref(!1),u=e.ref(60);e.onLoad((e=>{e.code&&(d.code=e.code,p.code=e.code)}));const l=()=>{d.agreedToTerms=!d.agreedToTerms},h=async()=>{if(!v.value)if(d.phone)if(t.checkPhone(d.phone))try{const e={phone:d.phone,verType:d.verType};await n.sendVerCode(e),s.show("验证码已发送"),m()}catch(e){s.show("发送验证码失败")}else s.show("请输入正确的手机号");else s.show("请输入手机号")},m=()=>{v.value=!0,u.value=60;const e=setInterval((()=>{u.value--,u.value<=0&&(clearInterval(e),v.value=!1)}),1e3)},w=async()=>{if(!d.phone)return void s.show("请输入手机号");if(!d.verCode)return void s.show("请输入验证码");if(!d.agreedToTerms)return void s.show("请阅读并同意服务协议和隐私政策");if("1"===(await n.bindWxLogin(d)).bindStatus){s.show("绑定成功，登录中...");try{await c.dispatch("Login",{userInfo:p}),await c.dispatch("GetInfo",{isJump:!0})}catch(o){e.index.__f__("error","at pages/login/bindPhone.vue:153",o)}}else s.show("绑定成功，请等待平台审核...")},g=o=>{e.index.navigateTo({url:`/pages/transition/webView?url=${encodeURIComponent({service:"http://www.ndjykj.com/agreement/nuodun-service.html",privacy:"http://www.ndjykj.com/agreement/nuodun-policy.html"}[o])}&title=${{service:"服务协议",privacy:"隐私政策"}[o]}`})};return(n,r)=>({a:e.p({title:"绑定手机号",showLeft:!0,path:"/pages/login/login"}),b:o._imports_0,c:d.phone,d:e.o((e=>d.phone=e.detail.value)),e:d.verCode,f:e.o((e=>d.verCode=e.detail.value)),g:e.t(v.value?`${u.value}s后重试`:"获取验证码"),h:e.o(h),i:v.value?1:"",j:e.o(w),k:d.agreedToTerms,l:e.o(l),m:e.o((e=>g("service"))),n:e.o((e=>g("privacy"))),o:e.gei(n,"")})}};wx.createPage(a);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/bindPhone.js.map
