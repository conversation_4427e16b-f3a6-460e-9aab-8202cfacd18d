"use strict";const e=require("../../../common/vendor.js"),t=require("../../../common/assets.js"),o=require("../../../api/home/<USER>");if(!Array){e.resolveComponent("dict-tag")()}const s={__name:"teacher",setup(s,{expose:a}){const{proxy:r}=e.getCurrentInstance(),{st_follow_status:l,t_st_follow_type:n}=r.useDict("st_follow_status","t_st_follow_type"),i=e.ref(64),c=e.ref([]),_=(t,o={})=>{let s=t;if(Object.keys(o).length>0){s+=`?${Object.entries(o).map((([e,t])=>`${e}=${t}`)).join("&")}`}e.index.navigateTo({url:s})},p=async()=>{try{const e=await o.getFollowList({pageNum:1,pageSize:5});c.value=e.rows}catch(t){e.index.__f__("error","at pages/tabbar/home/<USER>","获取最近跟进列表失败",t)}};return e.onMounted((()=>{r&&r.$dict&&"function"==typeof r.$dict.loadDict&&r.$dict.loadDict(["st_follow_status","st_follow_type"]);const t=e.index.getSystemInfoSync();i.value=t.statusBarHeight+44,p()})),a({refresh:async()=>{try{return await Promise.all([p()]),!0}catch(t){return e.index.__f__("error","at pages/tabbar/home/<USER>","刷新渠道首页数据失败",t),!1}}}),(o,s)=>({a:t._imports_0$3,b:e.o((e=>_("/pages/home/<USER>/list"))),c:t._imports_1$1,d:e.o((e=>_("/pages/home/<USER>/list"))),e:e.o((e=>_("/pages/home/<USER>/list"))),f:e.f(c.value,((t,o,s)=>e.e$1({a:e.t(t.stName),b:e.t(t.createTime),c:t.stFollowType},t.stFollowType?{d:"acd3028e-0-"+s,e:e.p({options:e.unref(n),value:t.stFollowType})}:{},{f:t.stFollowStatus},t.stFollowStatus?{g:"acd3028e-1-"+s,h:e.p({options:e.unref(l),value:t.stFollowStatus,showTag:!0,maxLength:8})}:{},{i:o,j:e.o((e=>_("/pages/home/<USER>/detail",{id:t.id})),o)}))),g:e.gei(o,"")})}},a=e._export_sfc(s,[["__scopeId","data-v-acd3028e"]]);wx.createComponent(a);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/tabbar/home/<USER>
