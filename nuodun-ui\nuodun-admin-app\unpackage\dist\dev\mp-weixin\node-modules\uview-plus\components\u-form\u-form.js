"use strict";const e=require("../../../../common/vendor.js");e.Schema.warning=function(){};const t={name:"u-form",mixins:[e.mpMixin,e.mixin,e.props$4],provide(){return{uForm:this}},data:()=>({formRules:{},validator:{},originalModel:null}),watch:{rules:{immediate:!0,handler(e){this.setRules(e)}},propsChange(e){var t;(null==(t=this.children)?void 0:t.length)&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler(t){this.originalModel||(this.originalModel=e.deepClone(t))}}},computed:{propsChange(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created(){this.children=[]},methods:{setRules(t){0!==Object.keys(t).length&&(0!==Object.keys(this.model).length?(this.formRules=t,this.validator=new e.Schema(t)):e.error("设置rules，model必须设置！如果已经设置，请刷新页面。"))},resetFields(){this.resetModel()},resetModel(t){this.children.map((t=>{const r=null==t?void 0:t.prop,i=e.getProperty(this.originalModel,r);e.setProperty(this.model,r,i)}))},clearValidate(e){e=[].concat(e),this.children.map((t=>{(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},async validateField(t,r,i=null,o){this.$nextTick((()=>{const s=[];t=[].concat(t);let l=this.children.map((r=>new Promise(((l,n)=>{const a=[];if(t.includes(r.prop)){const t=e.getProperty(this.model,r.prop),n=r.prop.split("."),h=n[n.length-1];let d=[];if(d=r.itemRules&&r.itemRules.length>0?r.itemRules:this.formRules[r.prop],!d)return void l();const c=[].concat(d);c.length||l();for(let u=0;u<c.length;u++){const n=c[u],d=[].concat(null==n?void 0:n.trigger);if(i&&!d.includes(i)){l();continue}new e.Schema({[h]:n}).validate({[h]:t},((t,i)=>{var n;e.test.array(t)&&(t.forEach((e=>{e.prop=r.prop})),s.push(...t),a.push(...t)),o&&1!=(null==o?void 0:o.showErrorMsg)||(r.message=(null==(n=a[0])?void 0:n.message)?a[0].message:null),u==c.length-1&&l(s)}))}}else l({})}))));Promise.all(l).then((e=>{"function"==typeof r&&r(s)})).catch((t=>{e.index.__f__("error","at node_modules/uview-plus/components/u-form/u-form.vue:218","An error occurred:",t)}))}))},validate(t){if(0!==Object.keys(this.formRules).length)return new Promise(((r,i)=>{this.$nextTick((()=>{const o=this.children.map((e=>e.prop));this.validateField(o,(t=>{t.length?("toast"===this.errorType&&e.toast(t[0].message),i(t)):r(!0)}),null,t)}))}));e.error("未设置rules，请看文档说明！如果已经设置，请刷新页面。")}}};const r=e._export_sfc(t,[["render",function(t,r,i,o,s,l){return{a:e.gei(t,"")}}]]);wx.createComponent(r);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/node-modules/uview-plus/components/u-form/u-form.js.map
