"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../store/store.js"),t=require("../../../../api/mine/userCommissionAgreement.js"),n=require("../../../../common/request.js"),l=require("../../../../common/modal.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../../../components/custom-nav/custom-nav.js")+(()=>"../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+r)();const r=()=>"../../../../components/htz-signature/htz-signature.js",o={__name:"agreementSign",setup(r){const{proxy:o}=e.getCurrentInstance(),u=o.toast,v=e.ref(null),i=e.ref(""),m=e.ref(!1),s=e.ref(!1),g={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},c=e.ref(""),d=e.ref(""),h=e.ref(""),p=e.ref(""),A=e.ref({sign:!1,seal:!1}),f=e.ref([]),P=e.ref(!1),C=e.ref(!1),_=e.ref(!1),w=e.ref({perAgreementPath:"",inteAgreementPath:"",corAgreementPath:"",agreementJson:"",perAgreementCode:"",inteAgreementCode:"",corAgreementCode:""}),x=e.ref(""),T=e.ref(null),M=e.computed((()=>{var e;return null==(e=a.store.state.sysInfo)?void 0:e.channelType})),y=e.computed((()=>"COMPANY"===M.value?h.value&&p.value:h.value)),N=e.computed((()=>A.value.sign||A.value.seal)),S=e.computed((()=>w.value.perAgreementPath||w.value.inteAgreementPath||w.value.corAgreementPath)),$=()=>{T.value&&T.value.cleanup&&T.value.cleanup(),s.value=!1,setTimeout((()=>{e.index.pageScrollTo({scrollTop:0,duration:0})}),300)},j=(a,t)=>{n.upload({filePath:a,name:"file",formData:{fileBizType:"contract_sign_image"},success:e=>{e&&200===e.code&&e.data?(_.value=!0,"sign"===t?h.value=e.data.filePath:"seal"===t&&(p.value=e.data.filePath),w.value.perAgreementPath="",w.value.inteAgreementPath="",w.value.corAgreementPath="",w.value.agreementJson="",w.value.perAgreementCode="",w.value.inteAgreementCode="",w.value.corAgreementCode="",f.value=[],u.show("上传成功")):(_.value=!1,"sign"===t?c.value="":"seal"===t&&(d.value=""),u.show(e&&e.msg?e.msg:"上传失败"))},fail:a=>{_.value=!1,e.index.__f__("error","at pages/mine/agreements/chan/agreementSign.vue:304","上传请求失败:",a),"sign"===t?c.value="":"seal"===t&&(d.value=""),u.show("上传失败")},complete:()=>{"sign"===t?A.value.sign=!1:"seal"===t&&(A.value.seal=!1),e.index.hideLoading()}})},I=e=>{e&&e.tempFilePath&&("sign"===x.value?(c.value=e.tempFilePath,A.value.sign=!0):"seal"===x.value&&(d.value=e.tempFilePath,A.value.seal=!0),j(e.tempFilePath,x.value)),$()},L=a=>{e.index.__f__("error","at pages/mine/agreements/chan/agreementSign.vue:347","签名失败:",a),u.show("签名失败"),$()},O=()=>{_.value?l.createModal({title:"重新生成",content:"确定要重新生成协议文件吗？",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&(w.value={perAgreementPath:"",inteAgreementPath:"",corAgreementPath:"",agreementJson:"",perAgreementCode:"",inteAgreementCode:"",corAgreementCode:""},f.value=[],q())})):u.show("请重新上传签名或公章")},q=()=>{N.value?u.show("请等待图片上传完成"):y.value?(P.value=!0,e.index.showLoading("生成协议中..."),t.generateSignedAgreement(v.value,{bailNameImg:h.value,bailChapterImg:"COMPANY"===M.value?p.value:""}).then((e=>{200===e.code&&e.data?(_.value=!1,w.value={...w.value,...e.data},f.value=e.data.files||[],u.show("协议生成成功")):(_.value=!0,u.show(e.msg||"生成协议失败"))})).catch((a=>{_.value=!0,e.index.__f__("error","at pages/mine/agreements/chan/agreementSign.vue:422","生成协议失败:",a),u.show("生成协议失败")})).finally((()=>{P.value=!1,e.index.hideLoading()}))):u.show("COMPANY"===M.value?"请先上传签名和公章":"请先上传签名")},Y=(a,t)=>{a?e.index.navigateTo({url:`/pages/transition/filePreview?filePath=${encodeURIComponent(a)}&fileName=${encodeURIComponent(t||"协议文件")}`}):u.show("文件路径为空")},z=()=>{l.createModal({title:"确认提交",content:"确定要提交该协议吗？提交后将不可修改",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&F()}))},F=()=>{S.value?(C.value=!0,e.index.showLoading("提交中..."),t.updateComStatus({id:v.value,comStatus:"2",...w.value}).then((a=>{200===a.code?(u.show("协议提交成功"),setTimeout((()=>{e.index.navigateBack()}),500)):u.show(a.msg||"提交失败")})).catch((a=>{e.index.__f__("error","at pages/mine/agreements/chan/agreementSign.vue:484","提交协议失败:",a),u.show("提交失败")})).finally((()=>{C.value=!1,e.index.hideLoading()}))):u.show("请先生成协议")};return e.onLoad((a=>{a.id?(v.value=a.id,i.value=decodeURIComponent(a.agreementCode||"")):(u.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1e3))})),e.onUnload((()=>{})),(a,t)=>e.e$1({a:e.p({title:"协议签署",showLeft:!0,path:"/pages/mine/agreements/chan/agreementDetail?id="+v.value}),b:m.value},m.value?{c:e.p({status:"loading","content-text":g})}:e.e$1({d:e.t(i.value),e:e.t("COMPANY"===M.value?"请完成签名和盖章后提交":"请完成签名后提交"),f:c.value},c.value?{g:c.value}:{},{h:e.o((a=>{return t="sign",x.value=t,s.value=!0,void e.nextTick$1((()=>{T.value}));var t})),i:"COMPANY"===M.value},"COMPANY"===M.value?e.e$1({j:d.value},d.value?{k:d.value}:{},{l:e.o((a=>{return t="seal",void e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"],success:e=>{const a=e.tempFilePaths[0];"sign"===t?(c.value=a,A.value.sign=!0):"seal"===t&&(d.value=a,A.value.seal=!0),j(a,t)},fail:a=>{e.index.__f__("error","at pages/mine/agreements/chan/agreementSign.vue:251","选择图片失败:",a),u.show("选择图片失败")}});var t}))}):{},{m:w.value.perAgreementPath||w.value.inteAgreementPath||w.value.corAgreementPath},w.value.perAgreementPath||w.value.inteAgreementPath||w.value.corAgreementPath?e.e$1({n:e.o(O),o:w.value.perAgreementPath},w.value.perAgreementPath?e.e$1({p:w.value.perAgreementCode},w.value.perAgreementCode?{q:e.t(w.value.perAgreementCode)}:{},{r:e.o((e=>Y(w.value.perAgreementPath,"渠道合作协议")))}):{},{s:w.value.inteAgreementPath},w.value.inteAgreementPath?e.e$1({t:w.value.inteAgreementCode},w.value.inteAgreementCode?{v:e.t(w.value.inteAgreementCode)}:{},{w:e.o((e=>Y(w.value.inteAgreementPath,"渠道中介协议")))}):{},{x:w.value.corAgreementPath},w.value.corAgreementPath?e.e$1({y:w.value.corAgreementCode},w.value.corAgreementCode?{z:e.t(w.value.corAgreementCode)}:{},{A:e.o((e=>Y(w.value.corAgreementPath,"渠道课程协议")))}):{}):{},{B:!S.value},S.value?{}:{C:e.t(N.value?"图片上传中...":P.value?"生成中...":y.value?"生成协议":"COMPANY"===M.value?"请上传签名和公章":"请上传签名"),D:e.o(q),E:P.value||!y.value||N.value},{F:S.value},S.value?{G:e.t(C.value?"提交中...":"确认提交"),H:e.o(z),I:C.value}:{}),{J:s.value},s.value?{K:e.sr(T,"af3c5bf6-2",{k:"htzsignature"}),L:e.o(I),M:e.o(L),N:e.p({cid:"handWriteCanvas",autoRotate:!0,rotateAngle:270}),O:e.o($)}:{},{P:e.gei(a,"")})}};wx.createPage(o);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/mine/agreements/chan/agreementSign.js.map
