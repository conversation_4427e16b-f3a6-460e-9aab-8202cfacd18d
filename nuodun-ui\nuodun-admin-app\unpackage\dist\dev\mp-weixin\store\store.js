"use strict";const e=require("../common/vendor.js"),t=require("../api/login.js"),s=require("../config/token.js"),o=require("../api/dicts.js"),n=require("../common/utils.js"),r=e.createStore({state:{token:s.getToken(),permissions:e.index.getStorageSync("permissions")||["test"],roles:e.index.getStorageSync("roles")||["test"],userInfo:e.index.getStorageSync("userInfo")||{roleType:"NORM"},sysInfo:e.index.getStorageSync("sysInfo")||{},dict:{}},mutations:{update(t,[s,o]){t[s]=o,e.index.setStorageSync(s,o)},setDict(e,{type:t,data:s}){e.dict[t]=s}},actions:{async Login({commit:e},{userInfo:o,isJump:n=!0}){o.username&&(o.username=o.username.trim());try{const r=await t.login(o,n);return s.setToken(r.token),e("update",["token",r.token]),r}catch(r){throw r}},GetInfo:({commit:s,state:o},{isJump:r=!0}={})=>new Promise(((o,a)=>{t.getInfo().then((t=>{const a=t.user;if(a.roleType||(a.roleType="NORM"),t.roles&&t.roles.length>0?(s("update",["roles",t.roles]),s("update",["permissions",t.permissions])):s("update",["roles","ROLE_DEFAULT"]),s("update",["userInfo",a]),s("update",["sysInfo",t.sysInfo]),r){const t=e.index.getStorageSync("wx_state");if(t){const[s,o]=t.split("_");if(n.pageRedirectConfig[s]){const t=n.pageRedirectConfig[s].paramsHandler(o);e.index.reLaunch(t)}else e.index.reLaunch({url:"/pages/tabbar/tabbar"})}else e.index.reLaunch({url:"/pages/tabbar/tabbar"})}else o(t)})).catch((e=>{a(e)}))})),LogOut:({commit:o,state:n})=>new Promise(((o,r)=>{t.logout(n.token).then((()=>{s.removeToken(),e.index.removeStorageSync("permissions"),e.index.removeStorageSync("roles"),e.index.removeStorageSync("userInfo"),o(),e.index.reLaunch({url:"/pages/login/login"})})).catch((e=>{r(e)}))})),getDicts({commit:e,state:t},s){s&&0!==s.length&&s.forEach((s=>{t.dict[s]||o.getDicts(s).then((t=>{t&&t.data?e("setDict",{type:s,data:t.data}):e("setDict",{type:s,data:[]})})).catch((()=>{e("setDict",{type:s,data:[]})}))}))}}});exports.store=r;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/store.js.map
