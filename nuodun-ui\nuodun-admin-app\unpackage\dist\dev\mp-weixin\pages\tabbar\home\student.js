"use strict";const t=require("../../../common/vendor.js"),e=require("../../../api/home/<USER>"),a={__name:"student",setup(a,{expose:r}){const s=t.ref({}),n=t.ref({}),u=t.computed((()=>{const t=new Date;return`${t.getFullYear()}年${t.getMonth()+1}月${t.getDate()}日`})),i=async()=>{const a=await e.getOwnerInfo();s.value=a.data,n.value=t.index.getStorageSync("userInfo")||{}},l=t=>t?t.split(" ")[0].replace(/-/g,"/"):"暂无日期",o=t=>{switch(t){case"1":return"待定校确认";case"2":return"已确认定校";case"3":return"已退回定校";case"4":return"定校被驳回";default:return"未知状态"}},c=t=>{switch(t){case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},m=t=>{switch(t){case"1":return"待签名确认";case"2":return"已签名确认";case"3":return"签名已退回";case"4":return"协议被驳回";default:return"未知状态"}},g=t=>{switch(t){case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},d=()=>{t.index.navigateTo({url:`/pages/home/<USER>/materialUpload?stId=${s.value.stId}`})};return t.onMounted((()=>{i()})),r({refresh:async()=>{try{return await i(),!0}catch(e){return t.index.__f__("error","at pages/tabbar/home/<USER>","刷新学生首页数据失败",e),!1}}}),(e,a)=>t.e$1({a:t.t(n.value.nickName||"同学"),b:t.t(u.value),c:s.value.agreementList&&s.value.agreementList.length>0},s.value.agreementList&&s.value.agreementList.length>0?{d:t.f(s.value.agreementList,((e,a,r)=>t.e$1({a:"0"!==e.agreeStatus},"0"!==e.agreeStatus?t.e$1({b:t.t(e.stuAgreementCode||"暂无"),c:t.t(l(e.createTime)),d:"1"==e.agreeStatus||"4"==e.agreeStatus},"1"==e.agreeStatus||"4"==e.agreeStatus?{e:t.t("1"==e.agreeStatus?"去签名确认":"查看驳回原因"),f:t.n(g(e.agreeStatus))}:{g:t.t(m(e.agreeStatus)),h:t.n(g(e.agreeStatus))},{i:t.o((a=>(e=>{t.index.navigateTo({url:"/pages/mine/agreements/stInfo/contract/agreementDetail?id="+e.id})})(e)),a)}):{},{j:e.stContractType.startsWith("S-")&&"0"!==e.confirmationStatus},e.stContractType.startsWith("S-")&&"0"!==e.confirmationStatus?t.e$1({k:t.t(e.confirmationCode||"暂无"),l:t.t(l(e.createTime)),m:"1"==e.confirmationStatus||"4"==e.confirmationStatus},"1"==e.confirmationStatus||"4"==e.confirmationStatus?{n:t.t("1"==e.confirmationStatus?"去定校确认":"查看驳回原因"),o:t.n(c(e.confirmationStatus))}:{p:t.t(o(e.confirmationStatus)),q:t.n(c(e.confirmationStatus))},{r:t.o((a=>(e=>{t.index.navigateTo({url:"/pages/mine/agreements/stInfo/school/agreementDetail?id="+e.id})})(e)),a)}):{},{s:e.stContractType.startsWith("S-")&&s.value.stMaterial&&"0"!==s.value.stMaterial.stFileStatus},e.stContractType.startsWith("S-")&&s.value.stMaterial&&"0"!==s.value.stMaterial.stFileStatus?t.e$1({t:"1"===s.value.stMaterial.stFileStatus},(s.value.stMaterial.stFileStatus,{}),{v:"2"===s.value.stMaterial.stFileStatus},(s.value.stMaterial.stFileStatus,{}),{w:"1"===s.value.stMaterial.stFileStatus&&s.value.stMaterial.stFileRemark},"1"===s.value.stMaterial.stFileStatus&&s.value.stMaterial.stFileRemark?{x:t.t(s.value.stMaterial.stFileRemark)}:{},{y:t.o(d,a)}):{},{z:a})))}:{},{e:t.gei(e,"")})}},r=t._export_sfc(a,[["__scopeId","data-v-20356d83"]]);wx.createComponent(r);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/tabbar/home/<USER>
