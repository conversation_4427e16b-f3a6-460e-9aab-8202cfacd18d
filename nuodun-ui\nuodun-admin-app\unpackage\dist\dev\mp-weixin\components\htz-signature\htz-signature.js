"use strict";const e=require("../../common/vendor.js");Math||t();const t=()=>"../gz-image-rotate/gz-image-rotate.js",a={__name:"htz-signature",props:{cid:{type:String,default:"signature-canvas"},autoRotate:{type:Boolean,default:!1},rotateAngle:{type:Number,default:90,validator:e=>[90,180,270].includes(e)}},emits:["sumbit","fail","cancel"],setup(t,{expose:a,emit:n}){const{proxy:l}=e.getCurrentInstance(),u=l.toast,i=t,o=n,s=e.ref([]),r=e.ref(null),v=e.ref(0),c=e.ref(0),d=e.ref(0),h=e.ref(0),g=e.ref(0),f=e.ref(0);e.ref(""),e.ref([]);const p=e.ref(null);let m=!1;const x=e.computed((()=>s.value.length>0));e.onMounted((()=>{e.nextTick$1((()=>{e.index.getSystemInfo({success:t=>{v.value=t.windowWidth-40,c.value=t.windowHeight-200,f.value=85,g.value=30,setTimeout((()=>{e.index.createSelectorQuery().in(this).select(`#${i.cid}`).boundingClientRect((t=>{t&&(d.value=t.left,h.value=t.top,setTimeout((()=>{try{r.value=e.index.createCanvasContext(i.cid,this),setTimeout((()=>{_()}),100)}catch(t){}}),100))})).exec()}),100)}})}))})),e.onBeforeUnmount((()=>{y()}));const y=()=>{},_=()=>{try{if(!r.value&&(r.value=e.index.createCanvasContext(i.cid,this),!r.value))return void setTimeout((()=>{r.value=e.index.createCanvasContext(i.cid,this),r.value&&C()}),500);C()}catch(t){e.index.__f__("error","at components/htz-signature/htz-signature.vue:198","Error in initCanvasSafe:",t)}},C=()=>{try{if(!r.value)return void e.index.__f__("error","at components/htz-signature/htz-signature.vue:206","Canvas context is null in initCanvas");r.value.fillStyle="rgba(255, 255, 255, 0)",r.value.fillRect(0,0,v.value,c.value),T(),r.value.draw(!1,(()=>{}))}catch(t){e.index.__f__("error","at components/htz-signature/htz-signature.vue:220","Error in initCanvas:",t)}},T=()=>{r.value.save(),r.value.setFontSize(18),r.value.setFillStyle("rgba(200, 200, 200, 0.3)"),r.value.setTextAlign("center"),r.value.setTextBaseline("middle"),r.value.restore()},w=()=>{x.value?(e.index.showLoading("生成中..."),e.index.canvasToTempFilePath({canvasId:i.cid,fileType:"png",quality:1,destWidth:2*v.value,destHeight:2*c.value,success:e=>{if(i.autoRotate){let t=1;180===i.rotateAngle&&(t=2),270===i.rotateAngle&&(t=3),p.value.start(e.tempFilePath,t)}else o("sumbit",e)},fail:t=>{e.index.hideLoading(),o("fail",t),u.show("生成签名失败")}},this)):u.show("请先签名")},b=t=>{e.index.hideLoading(),o("sumbit",t)},z=()=>{s.value=[],r.value.clearRect(0,0,v.value,c.value),T(),r.value.draw()},S=()=>{s.value.length>0?(s.value.pop(),L()):u.show("没有可撤销的笔画")},L=()=>{r.value.clearRect(0,0,v.value,c.value),T(),s.value.forEach((e=>{e.points.length>1&&(r.value.beginPath(),r.value.setLineCap("round"),r.value.setLineJoin("round"),r.value.setStrokeStyle("#000000"),r.value.setLineWidth(3),e.points.forEach(((e,t)=>{0===t?r.value.moveTo(e.x,e.y):r.value.lineTo(e.x,e.y)})),r.value.stroke())})),r.value.draw()},k=e=>{if(!r.value||m)return;m=!0,setTimeout((()=>{m=!1}),50);const t=E(e);s.value.push({points:[t]})},R=e=>{if(0===s.value.length||!r.value)return;const t=E(e),a=s.value[s.value.length-1];a.points.push(t),F(a)},A=e=>{s.value.length>0&&s.value[s.value.length-1].points.length<2&&s.value.pop()},E=e=>{const t=e.touches[0];return{x:t.x-d.value+g.value,y:t.y-h.value+f.value,type:e.type}},F=t=>{if(t.points.length<2)return;if(!r.value)return void e.index.__f__("warn","at components/htz-signature/htz-signature.vue:404","Canvas context is not ready yet");const a=t.points.length-1,n=t.points[a-1],l=t.points[a];r.value.beginPath(),r.value.setLineCap("round"),r.value.setLineJoin("round"),r.value.setStrokeStyle("#000000"),r.value.setLineWidth(3),r.value.moveTo(n.x,n.y),r.value.lineTo(l.x,l.y),r.value.stroke(),r.value.draw(!0)};return a({sumbit:w,clear:z,revoke:S,cancel:()=>{o("cancel")}}),(a,n)=>({a:t.cid,b:t.cid,c:e.o(k),d:e.o(R),e:e.o(A),f:e.o(S),g:e.o(z),h:e.n(x.value?"":"disabled"),i:e.o(w),j:e.sr(p,"0ba6dca0-0",{k:"imageRotateRef"}),k:e.o(b),l:e.gei(a,"")})}};wx.createComponent(a);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/htz-signature/htz-signature.js.map
