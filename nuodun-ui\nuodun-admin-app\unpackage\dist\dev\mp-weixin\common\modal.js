"use strict";const n=require("./vendor.js");exports.createModal=function(e){return new Promise((c=>{e.showInput?(n.index.showToast({title:"当前环境不支持输入对话框，请在H5环境使用",icon:"none",duration:2e3}),n.index.showModal({title:e.title||"提示",content:e.content||"",confirmText:e.confirmText||"确定",cancelText:e.cancelText||"取消",showCancel:!1!==e.showCancel,success:function(n){c({confirm:n.confirm,cancel:n.cancel,input:""})}})):n.index.showModal({title:e.title||"提示",content:e.content||"",confirmText:e.confirmText||"确定",cancelText:e.cancelText||"取消",showCancel:!1!==e.showCancel,success:function(n){c({confirm:n.confirm,cancel:n.cancel,input:""})}})}))};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/modal.js.map
