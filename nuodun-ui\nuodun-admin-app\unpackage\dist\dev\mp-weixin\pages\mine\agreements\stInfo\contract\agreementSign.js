"use strict";const e=require("../../../../../common/vendor.js"),a=require("../../../../../api/home/<USER>"),t=require("../../../../../common/request.js"),r=require("../../../../../common/modal.js"),u=require("../../../../../common/validate.js"),n=require("../../../../../config/environment.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-load-more")+e.resolveComponent("up-input")+e.resolveComponent("up-form-item")+e.resolveComponent("up-form")+e.resolveComponent("uni-data-picker"))()}Math||((()=>"../../../../../components/custom-nav/custom-nav.js")+(()=>"../../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../../../node-modules/uview-plus/components/u-form-item/u-form-item.js")+(()=>"../../../../../node-modules/uview-plus/components/u-form/u-form.js")+(()=>"../../../../../uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.js")+l+o)();const l=()=>"../../../../../components/document-preview/index.js",o=()=>"../../../../../components/htz-signature/htz-signature.js",d="190rpx",i={__name:"agreementSign",setup(l){const{proxy:o}=e.getCurrentInstance(),i=o.toast,s=n.environment.fileUrl||"",v=e.ref(null),g=e.ref(""),m=e.ref(!1),c=e.ref(!1),h={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},p=e.ref(null),f=e.ref(""),b=e.ref(""),P=e.ref(""),C=e.ref(""),y=e.ref({trustee:!1,guardian:!1}),w=e.ref(!1),I=e.ref(!1),N=e.ref(!1),A=e.ref(!0),_=e.ref(!1),x=e.ref([{text:"父亲",value:"父亲"},{text:"母亲",value:"母亲"},{text:"祖父",value:"祖父"},{text:"祖母",value:"祖母"},{text:"外祖父",value:"外祖父"},{text:"外祖母",value:"外祖母"},{text:"兄长",value:"兄长"},{text:"姐姐",value:"姐姐"},{text:"其他亲属",value:"其他亲属"}]),q=e.ref({stuAgreementPath:"",agreementJson:""}),W=e.ref(null),k=e.ref(null),R=e.ref(null),B=e.ref({serviceContent:"",serviceFeeLowercase:"",serviceFeeUppercase:"",trusteeName:"",trusteeIdCard:"",trusteePostalCode:"",trusteeAddress:"",trusteePhone:"",trusteeWechat:"",guardianName:"",guardianRelationship:"",guardianIdCard:"",guardianAddress:"",guardianPhone:"",guardianWechat:"",emergencyContactName:"",emergencyContactPhone:"",emergencyContactNameAndPhone:"",countryOrRegion:""}),j={trusteeName:{type:"string",required:!0,message:"请填写委托人姓名",trigger:["blur","change"]},trusteeIdCard:{type:"string",required:!0,validator:(e,a,t)=>{a?u.checkIdCard(a,!1)?t():t("委托人身份证格式不正确"):t("请填写委托人身份证")},trigger:["blur","change"]},trusteePostalCode:{type:"string",required:!0,message:"请填写邮政编码",trigger:["blur","change"]},trusteeAddress:{type:"string",required:!0,message:"请填写详细地址",trigger:["blur","change"]},trusteePhone:{type:"string",required:!0,validator:(e,a,t)=>{a?u.checkPhone(a,!1)?t():t("委托人电话格式不正确"):t("请填写委托人联系电话")},trigger:["blur","change"]},trusteeWechat:{type:"string",required:!0,message:"请填写微信号",trigger:["blur","change"]},emergencyContactName:{type:"string",required:!0,message:"请填写紧急联系人姓名",trigger:["blur","change"]},emergencyContactPhone:{type:"string",required:!0,validator:(e,a,t)=>{a?u.checkPhone(a,!1)?t():t("紧急联系人电话格式不正确"):t("请填写紧急联系人电话")},trigger:["blur","change"]}},S={guardianName:{type:"string",required:!0,message:"请填写监护人姓名",trigger:["blur","change"]},guardianRelationship:{type:"string",required:!0,message:"请选择监护人与委托人关系",trigger:["blur","change"]},guardianIdCard:{type:"string",required:!0,validator:(e,a,t)=>{a?u.checkIdCard(a,!1)?t():t("监护人身份证格式不正确"):t("请填写监护人身份证")},trigger:["blur","change"]},guardianAddress:{type:"string",required:!0,message:"请填写监护人详细地址",trigger:["blur","change"]},guardianPhone:{type:"string",required:!0,validator:(e,a,t)=>{a?u.checkPhone(a,!1)?t():t("监护人电话格式不正确"):t("请填写监护人联系电话")},trigger:["blur","change"]},guardianWechat:{type:"string",required:!0,message:"请填写监护人微信号",trigger:["blur","change"]}},V=e.ref(""),F=e.ref(null),L=e.ref(!1),T=e.ref(!1);e.watch(B,(()=>{_.value=!0,q.value.stuAgreementPath&&(q.value.stuAgreementPath=""),B.value.emergencyContactName&&B.value.emergencyContactPhone&&(B.value.emergencyContactNameAndPhone=`${B.value.emergencyContactName}：${B.value.emergencyContactPhone}`),J()}),{deep:!0}),e.watch(A,(()=>{_.value=!0,q.value.stuAgreementPath&&(q.value.stuAgreementPath="",i.show("年龄状态已变更，需要重新生成协议")),J()})),e.watch([P,C],(()=>{_.value=!0,q.value.stuAgreementPath&&(q.value.stuAgreementPath="",i.show("签名已更新，需要重新生成协议")),J()}));const J=()=>{clearTimeout(J._timer),J._timer=setTimeout((async()=>{try{let e=!1;if(W.value)try{e=await W.value.validate()}catch(a){e=!1}let t=!1;if(k.value)try{t=await k.value.validate()}catch(a){t=!1}let r=!0;if(!A.value&&R.value)try{r=await R.value.validate()}catch(a){r=!1}const u=!!P.value,n=A.value||!!C.value;L.value=e&&t&&r,T.value=L.value&&u&&n}catch(t){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:568","表单验证出错:",t),L.value=!1,T.value=!1}}),300)},O=e.computed((()=>T.value&&!U.value&&!w.value)),U=e.computed((()=>y.value.trustee||y.value.guardian)),$=e.computed((()=>q.value.stuAgreementPath));e.computed((()=>!w.value&&!U.value&&O.value)),e.computed((()=>_.value||N.value));const z=e=>{if(e){if(Object.keys(B.value).forEach((a=>{void 0!==e[a]&&(B.value[a]=e[a])})),e.emergencyContactNameAndPhone&&!B.value.emergencyContactName){const a=e.emergencyContactNameAndPhone.match(/(.+?)[:：]?\s*(\d+)/);a&&(B.value.emergencyContactName=a[1].trim(),B.value.emergencyContactPhone=a[2].trim())}_.value=!1}},M=()=>{p.value&&(p.value.trusteePhone&&u.checkPhone(p.value.trusteePhone),p.value.guardianPhone&&u.checkPhone(p.value.guardianPhone),p.value.trusteeIdCard&&u.checkIdCard(p.value.trusteeIdCard),p.value.guardianIdCard&&u.checkIdCard(p.value.guardianIdCard))},D=e=>{A.value=e,_.value=!0,$.value&&i.show("年龄状态已变更，需要重新生成协议")},E=(e,a)=>{if(null!=a){const t=a.toString().trim();B.value[e]=t}_.value=!0},G=e=>{if(e){const a=e.value[0];a&&(B.value.guardianRelationship=a,_.value=!0,R.value&&R.value.validateField("guardianRelationship"),q.value.stuAgreementPath&&(q.value.stuAgreementPath=""))}},H=a=>{V.value=a,c.value=!0,e.nextTick$1((()=>{F.value}))},K=()=>{F.value&&F.value.cleanup&&F.value.cleanup(),c.value=!1},Q=a=>{var r,u;a&&a.tempFilePath&&("trustee"===V.value?(f.value=a.tempFilePath,y.value.trustee=!0):"guardian"===V.value&&(b.value=a.tempFilePath,y.value.guardian=!0),r=a.tempFilePath,u=V.value,e.index.showLoading("上传中..."),t.upload({filePath:r,name:"file",formData:{fileBizType:"contract_agreement_image"},success:e=>{e&&200===e.code&&e.data?(N.value=!0,"trustee"===u?P.value=e.data.filePath:"guardian"===u&&(C.value=e.data.filePath),q.value.stuAgreementPath?(q.value.stuAgreementPath="",_.value=!0,i.show("签名已更新，需要重新生成协议")):i.show("上传成功")):(N.value=!1,"trustee"===u?f.value="":"guardian"===u&&(b.value=""),i.show(e&&e.msg?e.msg:"上传失败"))},fail:a=>{N.value=!1,e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:899","上传请求失败:",a),"trustee"===u?f.value="":"guardian"===u&&(b.value=""),i.show("上传失败")},complete:()=>{"trustee"===u?y.value.trustee=!1:"guardian"===u&&(y.value.guardian=!1),e.index.hideLoading(),e.nextTick$1((()=>{}))}})),K()},X=a=>{e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:947","签名失败:",a),i.show("签名失败"),K()},Y=()=>{N.value?r.createModal({title:"重新生成",content:"确定要重新生成协议文件吗？",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&(q.value.stuAgreementPath="",Z())})):i.show("请重新上传签名")},Z=async()=>{if(!(await((a=!0)=>new Promise((async t=>{try{if(await J(),!T.value)return a&&(P.value?A.value||C.value?i.show("请完善表单信息"):i.show("请完成监护人签名"):i.show("请完成委托人签名")),void t(!1);t(!0)}catch(r){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:729","表单验证异常",r),a&&i.show("表单验证出错"),t(!1)}})))()))return;if(U.value)return void i.show("请等待图片上传完成");w.value=!0,e.index.showLoading("生成协议中...");const t={...B.value,trusteeNameImg:P.value};A.value?(t.guardianName="",t.guardianRelationship="",t.guardianIdCard="",t.guardianAddress="",t.guardianPhone="",t.guardianWechat="",t.guardianNameImg=""):t.guardianNameImg=C.value,a.generateSignedContract(v.value,t).then((e=>{200===e.code&&e.data?(N.value=!1,_.value=!1,q.value={...q.value,...e.data},i.show("协议生成成功")):i.show(e.msg||"生成协议失败")})).catch((a=>{N.value=!0,e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:1033","生成协议失败:",a),i.show("生成协议失败")})).finally((()=>{w.value=!1,e.index.hideLoading()}))},ee=()=>{_.value?i.show("表单已修改，请先生成协议"):r.createModal({title:"确认提交",content:"确定要提交该协议吗？提交后将不可修改",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&ae()}))},ae=()=>{if(!$.value)return void i.show("请先生成协议");if(_.value)return i.show("表单已修改，需要重新生成协议"),void Z();I.value=!0,e.index.showLoading("提交中...");const t={id:v.value,trusteeNameImg:P.value,agreeStatus:"2",...q.value};A.value?(t.guardianName="",t.guardianRelationship="",t.guardianIdCard="",t.guardianAddress="",t.guardianPhone="",t.guardianWechat="",t.guardianNameImg=""):t.guardianNameImg=C.value,a.confirmSignContract(t).then((a=>{i.show("协议提交成功"),setTimeout((()=>{e.index.navigateBack()}),500)})).catch((a=>{e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:1122","提交协议失败:",a)})).finally((()=>{I.value=!1,e.index.hideLoading()}))};return e.onLoad((t=>{t.id?(v.value=t.id,g.value=decodeURIComponent(t.agreementCode||""),(async t=>{m.value=!0;try{const u=await a.getContractAgreement(t);if(200===u.code&&u.data){const a=u.data;if(g.value=a.stuAgreementCode||"",a.agreementJson)try{p.value=JSON.parse(a.agreementJson),z(p.value),M()}catch(r){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:624","解析协议JSON失败",r)}a.trusteeNameImg&&(f.value=s+a.trusteeNameImg,P.value=a.trusteeNameImg),a.guardianNameImg&&(b.value=s+a.guardianNameImg,C.value=a.guardianNameImg),q.value.stuAgreementPath="",q.value.agreementJson=a.agreementJson||"",p.value&&p.value.guardianName&&(A.value=!1)}else i.show(u.msg||"获取协议详情失败")}catch(r){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/agreementSign.vue:651","获取协议详情失败",r),i.show("获取协议详情失败")}finally{m.value=!1}})(v.value).then((()=>{setTimeout((()=>{J()}),800)}))):(i.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1e3))})),e.onUnload((()=>{})),(a,t)=>e.e$1({a:e.p({title:"合同协议签署",showLeft:!0,path:"/pages/mine/agreements/stInfo/contract/agreementDetail?id="+v.value}),b:m.value},m.value?{c:e.p({status:"loading","content-text":h})}:e.e$1({d:e.t(g.value),e:B.value.serviceContent},B.value.serviceContent?{f:e.t(B.value.serviceContent)}:{},{g:B.value.countryOrRegion},B.value.countryOrRegion?{h:e.t(B.value.countryOrRegion)}:{},{i:B.value.serviceFeeLowercase},B.value.serviceFeeLowercase?{j:e.t(B.value.serviceFeeLowercase),k:e.t(B.value.serviceFeeUppercase)}:{},{l:e.o((e=>E("trusteeName",e))),m:e.o((e=>B.value.trusteeName=e)),n:e.p({placeholder:"请输入姓名",border:"none",modelValue:B.value.trusteeName}),o:e.p({label:"姓名",prop:"trusteeName",borderBottom:!0,labelWidth:d,required:!0}),p:e.o((e=>E("trusteeIdCard",e))),q:e.o((e=>B.value.trusteeIdCard=e)),r:e.p({placeholder:"请输入身份证号码",border:"none",modelValue:B.value.trusteeIdCard}),s:e.p({label:"身份证",prop:"trusteeIdCard",borderBottom:!0,labelWidth:d,required:!0}),t:e.o((e=>E("trusteePostalCode",e))),v:e.o((e=>B.value.trusteePostalCode=e)),w:e.p({placeholder:"请输入邮政编码",border:"none",modelValue:B.value.trusteePostalCode}),x:e.p({label:"邮政编码",prop:"trusteePostalCode",borderBottom:!0,labelWidth:d,required:!0}),y:e.o((e=>E("trusteeAddress",e))),z:e.o((e=>B.value.trusteeAddress=e)),A:e.p({placeholder:"请输入详细地址",border:"none",modelValue:B.value.trusteeAddress}),B:e.p({label:"地址",prop:"trusteeAddress",borderBottom:!0,labelWidth:d,required:!0}),C:e.o((e=>E("trusteePhone",e))),D:e.o((e=>B.value.trusteePhone=e)),E:e.p({placeholder:"请输入联系电话",border:"none",modelValue:B.value.trusteePhone}),F:e.p({label:"联系电话",prop:"trusteePhone",borderBottom:!0,labelWidth:d,required:!0}),G:e.o((e=>E("trusteeWechat",e))),H:e.o((e=>B.value.trusteeWechat=e)),I:e.p({placeholder:"请输入微信号",border:"none",modelValue:B.value.trusteeWechat}),J:e.p({label:"微信号",prop:"trusteeWechat",borderBottom:!0,labelWidth:d,required:!0}),K:e.sr(W,"f083f782-2",{k:"formRef"}),L:e.p({labelPosition:"left",model:B.value,rules:j}),M:e.o((e=>E("emergencyContactName",e))),N:e.o((e=>B.value.emergencyContactName=e)),O:e.p({placeholder:"请输入紧急联系人姓名",border:"none",modelValue:B.value.emergencyContactName}),P:e.p({label:"姓名",prop:"emergencyContactName",borderBottom:!0,labelWidth:d,required:!0}),Q:e.o((e=>E("emergencyContactPhone",e))),R:e.o((e=>B.value.emergencyContactPhone=e)),S:e.p({placeholder:"请输入紧急联系人电话",border:"none",modelValue:B.value.emergencyContactPhone}),T:e.p({label:"联系电话",prop:"emergencyContactPhone",borderBottom:!0,labelWidth:d,required:!0}),U:e.sr(k,"f083f782-15",{k:"emergencyFormRef"}),V:e.p({labelPosition:"left",model:B.value,rules:j}),W:A.value?1:"",X:e.o((e=>D(!0))),Y:A.value?"":1,Z:e.o((e=>D(!1))),aa:!A.value},A.value?{}:{ab:e.o((e=>E("guardianName",e))),ac:e.o((e=>B.value.guardianName=e)),ad:e.p({placeholder:"请输入监护人姓名",border:"none",modelValue:B.value.guardianName}),ae:e.p({label:"姓名",prop:"guardianName",borderBottom:!0,labelWidth:d,required:!0}),af:e.o(G),ag:e.o((e=>B.value.guardianRelationship=e)),ah:e.p({placeholder:"请选择","popup-title":"请选择与委托人关系",localdata:x.value,modelValue:B.value.guardianRelationship}),ai:e.p({label:"与委托人关系",prop:"guardianRelationship",borderBottom:!0,labelWidth:d,required:!0}),aj:e.o((e=>E("guardianIdCard",e))),ak:e.o((e=>B.value.guardianIdCard=e)),al:e.p({placeholder:"请输入监护人身份证号码",border:"none",modelValue:B.value.guardianIdCard}),am:e.p({label:"身份证",prop:"guardianIdCard",borderBottom:!0,labelWidth:d,required:!0}),an:e.o((e=>E("guardianAddress",e))),ao:e.o((e=>B.value.guardianAddress=e)),ap:e.p({placeholder:"请输入监护人详细地址",border:"none",modelValue:B.value.guardianAddress}),aq:e.p({label:"地址",prop:"guardianAddress",borderBottom:!0,labelWidth:d,required:!0}),ar:e.o((e=>E("guardianPhone",e))),as:e.o((e=>B.value.guardianPhone=e)),at:e.p({placeholder:"请输入监护人联系电话",border:"none",modelValue:B.value.guardianPhone}),av:e.p({label:"联系电话",prop:"guardianPhone",borderBottom:!0,labelWidth:d,required:!0}),aw:e.o((e=>E("guardianWechat",e))),ax:e.o((e=>B.value.guardianWechat=e)),ay:e.p({placeholder:"请输入监护人微信号",border:"none",modelValue:B.value.guardianWechat}),az:e.p({label:"微信号",prop:"guardianWechat",borderBottom:!0,labelWidth:d,required:!0}),aA:e.sr(R,"f083f782-20",{k:"guardianFormRef"}),aB:e.p({labelPosition:"left",model:B.value,rules:S})},{aC:f.value},f.value?{aD:f.value}:{},{aE:e.o((e=>H("trustee"))),aF:!A.value},A.value?{}:e.e$1({aG:b.value},b.value?{aH:b.value}:{},{aI:e.o((e=>H("guardian")))}),{aJ:q.value.stuAgreementPath},q.value.stuAgreementPath?{aK:e.o(Y),aL:e.p({"file-path":e.unref(s)+q.value.stuAgreementPath,"file-name":"合同协议","allow-download":!0})}:{},{aM:!$.value&&U.value},(!$.value&&U.value,{}),{aN:!$.value&&!U.value&&w.value},($.value||U.value||w.value,{}),{aO:!($.value||U.value||w.value||O.value)},($.value||U.value||w.value||O.value,{}),{aP:!$.value&&!U.value&&!w.value&&O.value},$.value||U.value||w.value||!O.value?{}:{aQ:e.o(Z)},{aR:$.value},$.value?{aS:e.t(I.value?"提交中...":"确认提交"),aT:e.o(ee),aU:I.value}:{}),{aV:c.value},c.value?{aW:e.sr(F,"f083f782-34",{k:"htzsignature"}),aX:e.o(Q),aY:e.o(X),aZ:e.p({cid:"handWriteCanvas",autoRotate:!0,rotateAngle:270}),ba:e.o(K)}:{},{bb:e.gei(a,"")})}};wx.createPage(i);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/contract/agreementSign.js.map
