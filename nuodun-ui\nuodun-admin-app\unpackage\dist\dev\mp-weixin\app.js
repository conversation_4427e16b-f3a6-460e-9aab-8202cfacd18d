"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js"),r=require("./mixins/wechat-pull-refresh.js"),o=require("./store/store.js");require("./api/request.js");const t=require("./common/toast.js"),n=require("./common/dict/init.js"),i=require("./common/auth.js"),s=require("./common/common.js"),a=require("./common/utils.js");require("./common/loading.js");const c=require("./common/dict/Dict.js");Math;const u={__name:"App",setup(o,{expose:t}){r.useWechatPullRefresh(),e.ref(!1),e.useStore();e.getCurrentInstance().appContext.config.globalProperties.toast;const n={async onLaunch(){["navigateTo","redirectTo","reLaunch","switchTab"].forEach((r=>{e.index.addInterceptor(r,{invoke(r){try{const o=r.url.split("?")[0];if("/"===o)return!0;const t=o.startsWith("/")?o.slice(1):o,n=function(r){if("/"===r||""===r)return!0;const o=r.startsWith("/")?r.slice(1):r;return[...e.e.pages,...(e.e.subPackages||[]).reduce(((e,r)=>{const o=r.root;return[...e,...r.pages.map((e=>({path:`${o}/${e.path}`})))]}),[])].some((e=>e.path===o||"/"+e.path===o||e.path===o+"/index"||e.path===o+"index"))}(t);if(!n)return e.index.navigateTo({url:"/pages/error/404",fail:r=>{e.index.__f__("error","at App.vue:219","导航到404页面失败:",r)}}),!1;const i=getCurrentPages(),s=i[i.length-1];return!s||s.route!==t}catch(o){return e.index.__f__("error","at App.vue:236","路由拦截器错误:",o),!1}},fail:r=>(r.errMsg&&r.errMsg.includes("page not found")&&e.index.navigateTo({url:"/pages/error/404",fail:r=>{e.index.__f__("error","at App.vue:246","导航到404页面失败:",r)}}),!1)})}))},onShow(){},onHide(){},onError(r){e.index.__f__("error","at App.vue:268","应用错误:",r)},onPageNotFound(r){e.index.__f__("error","at App.vue:273","页面不存在:",r),e.index.navigateTo({url:"/pages/error/404",fail:r=>{e.index.__f__("error","at App.vue:283","导航到404页面失败，尝试重定向:",r),e.index.redirectTo({url:"/pages/error/404",fail:()=>{e.index.__f__("error","at App.vue:287","重定向到404页面失败，尝试重新加载首页"),e.index.reLaunch({url:"/"})}})}})}};return n.onLaunch(),t(n),()=>{}}},p=()=>"./common/dict/DictTag/index.js",l=()=>"./components/custom-nav/custom-nav.js";function g(){const r=e.createSSRApp(u);return r.component("CustomNav",l),r.component("DictTag",p),r.config.globalProperties.$store=o.store,r.config.globalProperties.toast=t.toast,r.config.globalProperties.showConfirm=s.showConfirm,r.config.globalProperties.hasRole=i.hasRole,r.config.globalProperties.hasPerimission=i.hasPerimission,r.config.globalProperties.parseTime=a.parseTime,r.config.globalProperties.useDict=c.useDict,r.use(o.store).use(e.uviewPlus,(()=>({options:{config:{unit:"rpx"}}}))),n.DictData.install(r),{app:r}}g().app.mount("#app"),exports.createApp=g;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
