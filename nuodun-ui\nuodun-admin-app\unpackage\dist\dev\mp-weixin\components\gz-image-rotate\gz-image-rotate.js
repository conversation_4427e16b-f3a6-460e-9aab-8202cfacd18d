"use strict";const e=require("../../common/vendor.js"),a={__name:"gz-image-rotate",emits:["success","fail"],setup(a,{expose:l,emit:u}){const t=e.ref(null),v=e.ref("canvas_id"),r=e.ref(null),s=e.ref(0),o=e.ref(0),n=e.ref(0),i=e.ref(0),c=e.ref(0),m=e.ref([]),d=u;t.value=e.index.createCanvasContext(v.value,this),e.onBeforeUnmount((()=>{f()}));const f=()=>{m.value.forEach((e=>{clearTimeout(e)})),m.value=[]},g=e=>{let a=t.value;a.drawImage(r.value,0,0,o.value,s.value),a.draw(),f();let l=setTimeout((()=>{_(e)}),400);m.value.push(l)},_=e=>{let a=t.value;switch(a.clearRect(0,0,o.value,s.value),e){case 1:n.value=90,c.value=-s.value,a.rotate(n.value*Math.PI/180),a.translate(i.value,c.value),a.drawImage(r.value,0,0,o.value,s.value),setTimeout((()=>{a.draw()}),400);let e=o.value;o.value=s.value,s.value=e;break;case 2:n.value=180,c.value=-s.value,i.value=-o.value,a.rotate(n.value*Math.PI/180),a.translate(i.value,c.value),a.drawImage(r.value,0,0,o.value,s.value),setTimeout((()=>{a.draw()}),400);break;case 3:n.value=-90,i.value=-o.value,a.rotate(n.value*Math.PI/180),a.translate(i.value,c.value),a.drawImage(r.value,0,0,o.value,s.value),setTimeout((()=>{a.draw()}),400);let l=o.value;o.value=s.value,s.value=l}f();let l=setTimeout((()=>{h()}),400);m.value.push(l)},h=()=>{e.index.canvasToTempFilePath({canvasId:v.value,success:e=>{d("success",e)},fail(a){e.index.__f__("error","at components/gz-image-rotate/gz-image-rotate.vue:131",a),d("fail",a)}},this)};return l({start:(a,l)=>{e.index.getImageInfo({src:a,success:e=>{let{width:a,height:u,path:t}=e;o.value=a,s.value=u,r.value=t,g(l)},fail(a){e.index.__f__("error","at components/gz-image-rotate/gz-image-rotate.vue:57",a)}})},init:()=>{f(),t.value=null,v.value="canvas_id",r.value=null,s.value=0,o.value=0,n.value=0,i.value=0,c.value=0,m.value=[]}}),(a,l)=>({a:v.value,b:o.value+"px",c:s.value+"px",d:e.gei(a,"")})}},l=e._export_sfc(a,[["__scopeId","data-v-5ea3ce14"]]);wx.createComponent(l);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/gz-image-rotate/gz-image-rotate.js.map
