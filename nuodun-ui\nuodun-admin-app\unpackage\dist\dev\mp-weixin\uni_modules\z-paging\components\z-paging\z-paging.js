"use strict";const e=require("./js/z-paging-main.js"),o=require("../../../../common/vendor.js"),a={},r=e=>{e.wxsCallMethods||(e.wxsCallMethods=[]),e.wxsCallMethods.push("_handleListTouchstart","_handleRefresherTouchstart","_handleTouchDirectionChange","_handleScrollViewBounce","_handleWxsPullingDown","_handleRefresherTouchmove","_handleRefresherTouchend","_handlePropUpdate","_handleWxsPullingDownStatusChange")};if(!Array){(o.resolveComponent("z-paging-refresh")+o.resolveComponent("z-paging-load-more")+o.resolveComponent("z-paging-empty-view"))()}Math,"function"==typeof a&&a(e._sfc_main),r(e._sfc_main);const t=o._export_sfc(e._sfc_main,[["render",function(e,a,r,t,l,s){return o.e$1({a:-1===e.cssSafeAreaInsetBottom},(e.cssSafeAreaInsetBottom,{}),{b:e.showF2&&e.showRefresherF2},e.showF2&&e.showRefresherF2?{c:o.o((()=>{})),d:o.s({transform:e.f2Transform,transition:"transform .2s linear",height:e.superContentHeight+"px","z-index":e.f2ZIndex})}:{},{e:e.zSlots.top},e.zSlots.top?o.e$1({f:!e.usePageScroll},e.usePageScroll?{g:o.o((()=>{})),h:o.s({top:`${e.windowTop}px`,"z-index":e.topZIndex})}:{}):{},{i:e.zSlots.left},e.zSlots.left?{j:e.finalIsOldWebView?1:""}:{},{k:e.finalRefresherFixedBacHeight>0},e.finalRefresherFixedBacHeight>0?{l:o.s({background:e.refresherFixedBackground,height:`${e.finalRefresherFixedBacHeight}px`})}:{},{m:e.showRefresher},e.showRefresher?o.e$1({n:e.useRefresherStatusBarPlaceholder},e.useRefresherStatusBarPlaceholder?{o:o.s({height:`${e.statusBarHeight}px`})}:{},{p:!(e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete||e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2)},e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete||e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2?{}:{q:o.r("refresher",{refresherStatus:e.refresherStatus})},{r:e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete},e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete||e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2||e.showCustomRefresher?{}:{v:o.sr("refresh","1aa372d7-0"),w:o.s({height:e.finalRefresherThreshold-e.finalRefresherThresholdPlaceholder+"px"}),x:o.p({status:e.refresherStatus,defaultThemeStyle:e.finalRefresherThemeStyle,defaultText:e.finalRefresherDefaultText,isIos:e.isIos,pullingText:e.finalRefresherPullingText,refreshingText:e.finalRefresherRefreshingText,completeText:e.finalRefresherCompleteText,goF2Text:e.finalRefresherGoF2Text,defaultImg:e.refresherDefaultImg,pullingImg:e.refresherPullingImg,refreshingImg:e.refresherRefreshingImg,completeImg:e.refresherCompleteImg,refreshingAnimated:e.refresherRefreshingAnimated,showUpdateTime:e.showRefresherUpdateTime,updateTimeKey:e.refresherUpdateTimeKey,updateTimeTextMap:e.finalRefresherUpdateTimeTextMap,imgStyle:e.refresherImgStyle,titleStyle:e.refresherTitleStyle,updateTimeStyle:e.refresherUpdateTimeStyle,unit:e.unit})},{s:e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2,t:!e.showCustomRefresher,y:o.s({height:`${e.finalRefresherThreshold}px`,background:e.refresherBackground}),z:o.s({"margin-top":`-${e.finalRefresherThreshold+e.refresherThresholdUpdateTag}px`,background:e.refresherBackground,opacity:e.isTouchmoving?1:0})}):{},{A:e.showLoading&&e.zSlots.loading&&!e.loadingFullFixed},(e.showLoading&&e.zSlots.loading&&e.loadingFullFixed,{}),{B:e.useVirtualList},e.useVirtualList?{C:o.s({height:e.virtualPlaceholderTopHeight+"px"})}:{},{D:e.finalUseInnerList},e.finalUseInnerList?o.e$1({E:e.finalUseVirtualList},e.finalUseVirtualList?{F:o.f(e.virtualList,((a,r,t)=>o.e$1(e.useCompatibilityMode?{}:{a:"cell-"+t,b:o.r("cell",{item:a,index:e.virtualTopRangeIndex+r},t)},{c:`${e.fianlVirtualCellIdPrefix}-${a[e.virtualCellIndexKey]}`,d:a.zp_unique_index,e:o.o((o=>e._innerCellClick(a,e.virtualTopRangeIndex+r)),a.zp_unique_index)}))),G:e.useCompatibilityMode,H:o.s(e.innerCellStyle)}:{I:o.f(e.realTotalData,((a,r,t)=>({a:"cell-"+t,b:o.r("cell",{item:a,index:r},t),c:r,d:o.o((o=>e._innerCellClick(a,r)),r)})))},{J:o.s(e.innerListStyle)}):{},{K:e.useChatRecordMode&&e.realTotalData.length>=e.defaultPageSize&&(e.loadingStatus!==e.M.NoMore||e.zSlots.chatNoMore)&&(e.realTotalData.length||e.showChatLoadingWhenReload&&e.showLoading)&&!e.isFirstPageAndNoMore},e.useChatRecordMode&&e.realTotalData.length>=e.defaultPageSize&&(e.loadingStatus!==e.M.NoMore||e.zSlots.chatNoMore)&&(e.realTotalData.length||e.showChatLoadingWhenReload&&e.showLoading)&&!e.isFirstPageAndNoMore?o.e$1({L:e.loadingStatus===e.M.NoMore&&e.zSlots.chatNoMore},e.loadingStatus===e.M.NoMore&&e.zSlots.chatNoMore?{}:o.e$1({M:e.zSlots.chatLoading},e.zSlots.chatLoading?{N:o.r("chatLoading",{loadingMoreStatus:e.loadingStatus})}:{O:o.o((o=>e._onLoadingMore("click"))),P:o.p({zConfig:e.zLoadMoreConfig})}),{Q:o.s(e.chatRecordRotateStyle)}):{},{R:e.useVirtualList},e.useVirtualList?{S:o.s({height:e.virtualPlaceholderBottomHeight+"px"})}:{},{T:e.showLoadingMoreDefault},e.showLoadingMoreDefault||e.showLoadingMoreLoading||e.showLoadingMoreNoMore||e.showLoadingMoreFail?{}:e.showLoadingMoreCustom?{Y:o.o((o=>e._onLoadingMore("click"))),Z:o.p({zConfig:e.zLoadMoreConfig})}:{},{U:e.showLoadingMoreLoading,V:e.showLoadingMoreNoMore,W:e.showLoadingMoreFail,X:e.showLoadingMoreCustom,aa:e.safeAreaInsetBottom&&e.finalUseSafeAreaPlaceholder&&!e.useChatRecordMode},e.safeAreaInsetBottom&&e.finalUseSafeAreaPlaceholder&&!e.useChatRecordMode?{ab:o.s({height:e.safeAreaBottom+"px"})}:{},{ac:o.s(e.finalPlaceholderTopHeightStyle),ad:o.s(e.finalPagingContentStyle),ae:e.showEmpty},e.showEmpty?o.e$1({af:e.zSlots.empty},e.zSlots.empty?{ag:o.r("empty",{isLoadFailed:e.isLoadFailed})}:{ah:o.o(e._emptyViewReload),ai:o.o(e._emptyViewClick),aj:o.p({emptyViewImg:e.finalEmptyViewImg,emptyViewText:e.finalEmptyViewText,showEmptyViewReload:e.finalShowEmptyViewReload,emptyViewReloadText:e.finalEmptyViewReloadText,isLoadFailed:e.isLoadFailed,emptyViewStyle:e.emptyViewStyle,emptyViewTitleStyle:e.emptyViewTitleStyle,emptyViewImgStyle:e.emptyViewImgStyle,emptyViewReloadStyle:e.emptyViewReloadStyle,emptyViewZIndex:e.emptyViewZIndex,emptyViewFixed:e.emptyViewFixed,unit:e.unit})},{ak:e.emptyViewCenter?1:"",al:o.s(e.emptyViewSuperStyle),am:o.s(e.chatRecordRotateStyle)}):{},{an:o.s({justifyContent:e.useChatRecordMode?"flex-end":"flex-start"}),ao:o.s(e.scrollViewInStyle),ap:o.s({transform:e.finalRefresherTransform,transition:e.refresherTransition}),aq:e.wxsPropType,ar:e.finalRefresherThreshold,as:e.refresherF2Enabled,at:e.finalRefresherF2Threshold,av:e.isIos,aw:e.loading||e.isRefresherInComplete,ax:e.useChatRecordMode,ay:e.finalRefresherEnabled,az:e.useCustomRefresher,aA:e.wxsPageScrollTop,aB:e.wxsScrollTop,aC:e.refresherMaxAngle,aD:e.refresherNoTransform,aE:e.refresherAngleEnableChangeContinued,aF:e.usePageScroll,aG:e.watchTouchDirectionChange,aH:e.isTouchmoving,aI:e.finalRefresherOutRate,aJ:e.finalRefresherPullRate,aK:e.hasTouchmove,aL:e.usePageScroll?"":1,aM:e.showScrollbar?"":1,aN:o.s(e.chatRecordRotateStyle),aO:e.scrollTop,aP:e.scrollLeft,aQ:e.scrollX,aR:e.finalScrollable,aS:e.finalEnableBackToTop,aT:e.showScrollbar,aU:e.finalScrollWithAnimation,aV:e.scrollIntoView,aW:e.finalLowerThreshold,aX:e.finalRefresherEnabled&&!e.useCustomRefresher,aY:e.finalRefresherThreshold,aZ:e.finalRefresherDefaultStyle,ba:e.refresherBackground,bb:e.finalRefresherTriggered,bc:o.o(((...o)=>e._scroll&&e._scroll(...o))),bd:o.o(((...o)=>e._onScrollToLower&&e._onScrollToLower(...o))),be:o.o(((...o)=>e._onScrollToUpper&&e._onScrollToUpper(...o))),bf:o.o(((...o)=>e._onRestore&&e._onRestore(...o))),bg:o.o((o=>e._onRefresh(!0))),bh:e.finalIsOldWebView?1:"",bi:o.s(e.scrollViewContainerStyle),bj:e.zSlots.right},e.zSlots.right?{bk:e.finalIsOldWebView?1:""}:{},{bl:e.usePageScroll?"":1,bm:o.s(e.finalScrollViewStyle),bn:e.zSlots.bottom},e.zSlots.bottom?o.e$1({bo:!e.usePageScroll},e.usePageScroll?o.e$1({bp:e.safeAreaInsetBottom},e.safeAreaInsetBottom?{bq:o.s({height:e.safeAreaBottom+"px"})}:{},{br:o.o((()=>{})),bs:o.s({bottom:`${e.windowBottom}px`,background:e.bottomBgColor})}):{}):{},{bt:e.safeAreaInsetBottom&&!e.usePageScroll&&!e.finalUseSafeAreaPlaceholder&&!e.useChatRecordMode},!e.safeAreaInsetBottom||e.usePageScroll||e.finalUseSafeAreaPlaceholder||e.useChatRecordMode?{}:{bv:o.s({height:e.safeAreaBottom+"px"})},{bw:e.useChatRecordMode&&e.autoAdjustPositionWhenChat},e.useChatRecordMode&&e.autoAdjustPositionWhenChat?{bx:o.s({height:e.chatRecordModeSafeAreaBottom+"px"}),by:o.s({height:e.keyboardHeight+"px"})}:{},{bz:e.bottomBgColor,bA:e.showBackToTopClass},e.showBackToTopClass?o.e$1({bB:e.zSlots.backToTop},e.zSlots.backToTop?{}:{bC:e.useChatRecordMode&&!e.backToTopImg.length?1:"",bD:e.backToTopImg.length?e.backToTopImg:e.base64BackToTop},{bE:o.n(e.finalBackToTopClass),bF:o.s(e.finalBackToTopStyle),bG:o.o(((...o)=>e._backToTopClick&&e._backToTopClick(...o)))}):{},{bH:e.showLoading&&e.zSlots.loading&&e.loadingFullFixed},(e.showLoading&&e.zSlots.loading&&e.loadingFullFixed,{}),{bI:o.n({"z-paging-content":!0,"z-paging-content-full":!e.usePageScroll,"z-paging-content-fixed":!e.usePageScroll&&e.fixed,"z-paging-content-page":e.usePageScroll,"z-paging-reached-top":e.renderPropScrollTop<1,"z-paging-use-chat-record-mode":e.useChatRecordMode}),bJ:o.n(e.pagingClass),bK:o.s(e.finalPagingStyle),bL:o.gei(e,"")})}],["__scopeId","data-v-1aa372d7"]]);wx.createComponent(t);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/z-paging.js.map
