{"version": 3, "file": "index.js", "sources": ["components/document-preview/index.vue", "../../../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay9naXREYXRhL251b2R1bi1ib290L251b2R1bi11aS9udW9kdW4tYWRtaW4tYXBwL2NvbXBvbmVudHMvZG9jdW1lbnQtcHJldmlldy9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"document-preview\">\r\n    <view class=\"file-card\">\r\n      <view class=\"file-card-info\">\r\n        <text class=\"file-card-icon\">{{ fileIcon }}</text>\r\n        <text class=\"file-card-name\">{{ fileName }}</text>\r\n        <text v-if=\"!isSupportedFileType\" class=\"file-type-tip\">暂不支持预览</text>\r\n      </view>\r\n      <view class=\"file-card-btn\" @tap=\"viewFile\" v-if=\"allowView && isSupportedFileType\">\r\n        查看\r\n      </view>\r\n      <view class=\"file-card-btn download-btn\" @tap=\"downloadFile\" v-if=\"allowDownload\">\r\n        下载\r\n      </view>\r\n      <view class=\"file-card-btn delete-btn\" @tap=\"deleteFile\" v-if=\"allowDelete\">\r\n        删除\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {computed, getCurrentInstance} from 'vue'\r\nimport {createModal} from '@/common/modal'\r\nimport {getFileType} from '@/common/fileUtils.js'\r\nimport {getFileTypeIcon} from '@/common/fileUtils'\r\n\r\n// 定义事件\r\nconst emit = defineEmits(['delete', 'save'])\r\nimport config from '@/config/environment'\r\nimport {downloadAndOpenFile} from '@/common/fileUtils'\r\n\r\nconst {proxy} = getCurrentInstance()\r\nconst toast = proxy.toast\r\n\r\nconst props = defineProps({\r\n  // 文件路径\r\n  filePath: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  // 文件名称\r\n  fileName: {\r\n    type: String,\r\n    default: '文件'\r\n  },\r\n  // 文件URL前缀\r\n  urlPrefix: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  // 是否允许查看\r\n  allowView: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  // 是否允许下载\r\n  allowDownload: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  // 是否允许删除\r\n  allowDelete: {\r\n    type: Boolean,\r\n    default: false\r\n  }\r\n})\r\n\r\n// 获取文件类型\r\nconst fileType = computed(() => {\r\n  return getFileType(props.filePath)\r\n})\r\n\r\n// 判断是否为支持的文件类型\r\nconst isSupportedFileType = computed(() => {\r\n  return ['image', 'document'].includes(fileType.value)\r\n})\r\n\r\n// 获取文件图标\r\nconst fileIcon = computed(() => {\r\n  return getFileTypeIcon(props.fileName)\r\n})\r\n\r\n// 获取文件完整URL\r\nconst fullFileUrl = computed(() => {\r\n  if (!props.filePath) return ''\r\n\r\n  // 如果filePath已经包含前缀，则直接返回\r\n  if (props.filePath.startsWith('http://') || props.filePath.startsWith('https://')) {\r\n    return props.filePath\r\n  }\r\n\r\n  // 使用传入的前缀或环境配置\r\n  return (props.urlPrefix || config.fileUrl || '') + props.filePath\r\n})\r\n\r\n// 查看文件\r\nconst viewFile = () => {\r\n  if (!props.filePath) {\r\n    toast.show('文件路径为空')\r\n    return\r\n  }\r\n\r\n  // 检查文件类型是否支持预览\r\n  if (!isSupportedFileType.value) {\r\n    const fileTypeName = getFileTypeName(fileType.value)\r\n    toast.show(`暂不支持打开 ${fileTypeName} 文件，请下载后查看`)\r\n    return\r\n  }\r\n\r\n  let filePath = props.filePath\r\n\r\n  // 如果不是完整的URL，拼接文件服务器地址\r\n  if (!filePath.startsWith('http')) {\r\n    filePath = fullFileUrl.value\r\n  }\r\n\r\n  // 如果是图片文件，使用图片预览\r\n  if (fileType.value === 'image') {\r\n    uni.previewImage({\r\n      urls: [filePath],\r\n      current: filePath,\r\n      fail: (err) => {\r\n        console.error('图片预览失败:', err)\r\n        toast.show('图片预览失败')\r\n      }\r\n    })\r\n  } else if (fileType.value === 'document') {\r\n    // 文档文件直接预览\r\n    previewDocument(filePath)\r\n  } else {\r\n    const fileTypeName = getFileTypeName(fileType.value)\r\n    toast.show(`暂不支持打开 ${fileTypeName} 文件，请下载后查看`)\r\n  }\r\n}\r\n\r\n// 预览文档文件\r\nconst previewDocument = (filePath) => {\r\n  // #ifdef H5\r\n  // H5环境检查是否在微信浏览器中\r\n  const isWechatBrowser = /micromessenger/i.test(navigator.userAgent)\r\n\r\n  if (isWechatBrowser) {\r\n    // 微信浏览器中直接打开文件，让微信处理预览\r\n    window.open(filePath, '_blank')\r\n  } else {\r\n    // 普通H5浏览器跳转到预览页面\r\n    uni.navigateTo({\r\n      url: `/pages/transition/filePreview?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(props.fileName)}`\r\n    })\r\n  }\r\n  // #endif\r\n\r\n  // #ifdef MP-WEIXIN\r\n  // 微信小程序使用 wx.openDocument 直接预览\r\n  uni.showLoading({\r\n    title: '准备预览...',\r\n    mask: true\r\n  })\r\n\r\n  uni.downloadFile({\r\n    url: filePath,\r\n    success: (res) => {\r\n      if (res.statusCode === 200) {\r\n        uni.openDocument({\r\n          filePath: res.tempFilePath,\r\n          showMenu: true,\r\n          success: () => {\r\n            uni.hideLoading()\r\n            console.log('文档预览成功')\r\n            // 预览成功后提示用户可以保存\r\n            setTimeout(() => {\r\n              uni.showToast({\r\n                title: '可点击右上角保存到手机',\r\n                icon: 'none',\r\n                duration: 3000\r\n              })\r\n            }, 1000)\r\n          },\r\n          fail: (err) => {\r\n            uni.hideLoading()\r\n            console.error('文档预览失败:', err)\r\n            toast.show('文档预览失败')\r\n          }\r\n        })\r\n      } else {\r\n        uni.hideLoading()\r\n        toast.show('文件下载失败')\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      uni.hideLoading()\r\n      console.error('文件下载失败:', err)\r\n      toast.show('文件下载失败')\r\n    }\r\n  })\r\n  // #endif\r\n\r\n  // #ifdef MP-ALIPAY\r\n  // 支付宝小程序使用类似的方式\r\n  uni.showLoading({\r\n    title: '准备预览...',\r\n    mask: true\r\n  })\r\n\r\n  uni.downloadFile({\r\n    url: filePath,\r\n    success: (res) => {\r\n      if (res.statusCode === 200) {\r\n        uni.openDocument({\r\n          filePath: res.tempFilePath,\r\n          success: () => {\r\n            uni.hideLoading()\r\n            console.log('文档预览成功')\r\n          },\r\n          fail: (err) => {\r\n            uni.hideLoading()\r\n            console.error('文档预览失败:', err)\r\n            toast.show('文档预览失败，请尝试下载')\r\n            downloadFile()\r\n          }\r\n        })\r\n      } else {\r\n        uni.hideLoading()\r\n        toast.show('文件下载失败')\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      uni.hideLoading()\r\n      console.error('文件下载失败:', err)\r\n      toast.show('文件下载失败')\r\n    }\r\n  })\r\n  // #endif\r\n\r\n  // #ifdef APP-PLUS\r\n  // APP环境也使用 openDocument\r\n  uni.showLoading({\r\n    title: '准备预览...',\r\n    mask: true\r\n  })\r\n\r\n  uni.downloadFile({\r\n    url: filePath,\r\n    success: (res) => {\r\n      if (res.statusCode === 200) {\r\n        uni.openDocument({\r\n          filePath: res.tempFilePath,\r\n          success: () => {\r\n            uni.hideLoading()\r\n            console.log('文档预览成功')\r\n          },\r\n          fail: (err) => {\r\n            uni.hideLoading()\r\n            console.error('文档预览失败:', err)\r\n            toast.show('文档预览失败，请尝试下载')\r\n            downloadFile()\r\n          }\r\n        })\r\n      } else {\r\n        uni.hideLoading()\r\n        toast.show('文件下载失败')\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      uni.hideLoading()\r\n      console.error('文件下载失败:', err)\r\n      toast.show('文件下载失败')\r\n    }\r\n  })\r\n  // #endif\r\n}\r\n\r\n// 获取文件类型名称\r\nconst getFileTypeName = (type) => {\r\n  const typeNames = {\r\n    'image': '图片',\r\n    'video': '视频',\r\n    'document': '文档',\r\n  }\r\n  return typeNames[type] || '此类型'\r\n}\r\n\r\n// 下载文件\r\nconst downloadFile = () => {\r\n  // #ifdef MP-WEIXIN\r\n  // 微信小程序环境：直接预览文件，提示用户保存\r\n  previewDocument(fullFileUrl.value)\r\n  // #endif\r\n\r\n  // #ifndef MP-WEIXIN\r\n  // 其他环境使用通用下载方法\r\n  downloadAndOpenFile(fullFileUrl.value)\r\n  // #endif\r\n}\r\n\r\n// 删除文件\r\nconst deleteFile = () => {\r\n  createModal({\r\n    title: '确认删除',\r\n    content: `确定要删除文件\"${props.fileName}\"吗？删除后将无法恢复`,\r\n    confirmText: '确定',\r\n    cancelText: '取消'\r\n  }).then(res => {\r\n    if (res.confirm) {\r\n      // 触发删除事件，传递文件信息给父组件\r\n      emit('delete', {\r\n        filePath: props.filePath,\r\n        fileName: props.fileName\r\n      })\r\n    }\r\n  }).catch(error => {\r\n    console.error('删除确认框异常:', error)\r\n  })\r\n}\r\n\r\n// 表单保存校验方法\r\nconst handleSave = async (data = {}, options = {}) => {\r\n  try {\r\n    // 默认配置\r\n    const defaultOptions = {\r\n      showConfirm: true,\r\n      validateRequired: true,\r\n      showLoading: true,\r\n      loadingText: '保存中...',\r\n      successText: '保存成功',\r\n      ...options\r\n    }\r\n\r\n    // 必填项校验\r\n    if (defaultOptions.validateRequired) {\r\n      const requiredFields = ['fileName', 'filePath']\r\n      const missingFields = []\r\n\r\n      requiredFields.forEach(field => {\r\n        const value = data[field] || props[field]\r\n        if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n          missingFields.push(field === 'fileName' ? '文件名称' : '文件路径')\r\n        }\r\n      })\r\n\r\n      if (missingFields.length > 0) {\r\n        toast.show(`请填写：${missingFields.join('、')}`)\r\n        return {success: false, message: '必填项校验失败'}\r\n      }\r\n    }\r\n\r\n    // 显示确认框\r\n    if (defaultOptions.showConfirm) {\r\n      const confirmResult = await createModal({\r\n        title: '确认保存',\r\n        content: '确定要保存当前文件信息吗？',\r\n        confirmText: '确定',\r\n        cancelText: '取消'\r\n      })\r\n\r\n      if (!confirmResult.confirm) {\r\n        return {success: false, message: '用户取消操作'}\r\n      }\r\n    }\r\n\r\n    // 显示加载状态\r\n    if (defaultOptions.showLoading) {\r\n      uni.showLoading({\r\n        title: defaultOptions.loadingText,\r\n        mask: true\r\n      })\r\n    }\r\n\r\n    // 准备保存数据\r\n    const saveData = {\r\n      fileName: data.fileName || props.fileName,\r\n      filePath: data.filePath || props.filePath,\r\n      urlPrefix: data.urlPrefix || props.urlPrefix,\r\n      ...data\r\n    }\r\n\r\n    // 触发保存事件，让父组件处理具体的保存逻辑\r\n    emit('save', {\r\n      data: saveData,\r\n      options: defaultOptions\r\n    })\r\n\r\n    // 隐藏加载状态\r\n    if (defaultOptions.showLoading) {\r\n      uni.hideLoading()\r\n    }\r\n\r\n    // 显示成功提示\r\n    if (defaultOptions.successText) {\r\n      toast.show(defaultOptions.successText)\r\n    }\r\n\r\n    return {success: true, data: saveData}\r\n  } catch (error) {\r\n    // 隐藏加载状态\r\n    uni.hideLoading()\r\n\r\n    console.error('保存校验异常:', error)\r\n    toast.show('保存失败，请重试')\r\n\r\n    return {success: false, message: error.message || '保存异常'}\r\n  }\r\n}\r\n\r\n// 导出方法供父组件调用\r\ndefineExpose({\r\n  viewFile,\r\n  deleteFile,\r\n  handleSave\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.document-preview {\r\n  width: 100%;\r\n}\r\n\r\n.file-card {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx 20rpx;\r\n  background-color: #f9fafc;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 16rpx;\r\n  border: 1px solid #eaecf0;\r\n\r\n  &:active {\r\n    background-color: #f0f2f5;\r\n  }\r\n}\r\n\r\n.file-card-info {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-card-icon {\r\n  font-size: 40rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.file-card-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  flex: 1;\r\n}\r\n\r\n.file-type-tip {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-left: 12rpx;\r\n  white-space: nowrap;\r\n}\r\n\r\n.file-card-btn {\r\n  font-size: 28rpx;\r\n  color: var(--nuodun-primary-color, #0078ff);\r\n  padding: 8rpx 20rpx;\r\n  background-color: rgba(var(--nuodun-primary-color-rgb, 0, 120, 255), 0.08);\r\n  border-radius: 8rpx;\r\n  white-space: nowrap;\r\n}\r\n\r\n.download-btn {\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.delete-btn {\r\n  margin-left: 10rpx;\r\n  color: #ff4d4f !important;\r\n  background-color: rgba(255, 77, 79, 0.08) !important;\r\n\r\n  &:active {\r\n    background-color: rgba(255, 77, 79, 0.15) !important;\r\n  }\r\n}\r\n</style>", "import Component from 'D:/work/gitData/nuodun-boot/nuodun-ui/nuodun-admin-app/components/document-preview/index.vue'\nwx.createComponent(Component)"], "names": ["emit", "__emit", "proxy", "getCurrentInstance", "toast", "props", "__props", "fileType", "computed", "getFileType", "filePath", "isSupportedFileType", "includes", "value", "fileIcon", "getFileTypeIcon", "fileName", "fullFileUrl", "startsWith", "urlPrefix", "config", "fileUrl", "viewFile", "show", "fileTypeName", "getFileTypeName", "uni", "previewImage", "urls", "current", "fail", "err", "__f__", "previewDocument", "showLoading", "title", "mask", "downloadFile", "url", "success", "res", "statusCode", "openDocument", "tempFile<PERSON>ath", "showMenu", "hideLoading", "setTimeout", "showToast", "icon", "duration", "type", "image", "video", "document", "deleteFile", "createModal", "content", "confirmText", "cancelText", "then", "confirm", "catch", "error", "__expose", "handleSave", "async", "data", "options", "defaultOptions", "showConfirm", "validateRequired", "loadingText", "successText", "missingFields", "for<PERSON>ach", "field", "trim", "push", "length", "join", "message", "saveData", "wx", "createComponent", "Component"], "mappings": "kdA4BA,MAAMA,EAAOC,GAIPC,MAACA,GAASC,uBACVC,EAAQF,EAAME,MAEdC,EAAQC,EAkCRC,EAAWC,EAAQA,UAAC,IACjBC,EAAWA,YAACJ,EAAMK,YAIrBC,EAAsBH,EAAQA,UAAC,IAC5B,CAAC,QAAS,YAAYI,SAASL,EAASM,SAI3CC,EAAWN,EAAQA,UAAC,IACjBO,EAAeA,gBAACV,EAAMW,YAIzBC,EAAcT,EAAQA,UAAC,IACtBH,EAAMK,SAGPL,EAAMK,SAASQ,WAAW,YAAcb,EAAMK,SAASQ,WAAW,YAC7Db,EAAMK,UAIPL,EAAMc,WAAaC,EAAAA,YAAOC,SAAW,IAAMhB,EAAMK,SAR7B,KAYxBY,EAAW,KACX,IAACjB,EAAMK,SAET,YADAN,EAAMmB,KAAK,UAKT,IAACZ,EAAoBE,MAAO,CACxB,MAAAW,EAAeC,EAAgBlB,EAASM,OAE9C,YADMT,EAAAmB,KAAK,UAAUC,cAEtB,CAED,IAAId,EAAWL,EAAMK,SAQjB,GALCA,EAASQ,WAAW,UACvBR,EAAWO,EAAYJ,OAIF,UAAnBN,EAASM,MACXa,EAAAA,MAAIC,aAAa,CACfC,KAAM,CAAClB,GACPmB,QAASnB,EACToB,KAAOC,IACLL,EAAAA,MAAcM,MAAA,QAAA,+CAAA,UAAWD,GACzB3B,EAAMmB,KAAK,kBAGnB,GAAgC,aAAnBhB,EAASM,MAElBoB,EAAgBvB,OACX,CACC,MAAAc,EAAeC,EAAgBlB,EAASM,OACxCT,EAAAmB,KAAK,UAAUC,cACtB,GAIGS,EAAmBvB,IAkBvBgB,EAAAA,MAAIQ,YAAY,CACdC,MAAO,UACPC,MAAM,IAGRV,EAAAA,MAAIW,aAAa,CACfC,IAAK5B,EACL6B,QAAUC,IACe,MAAnBA,EAAIC,WACNf,EAAAA,MAAIgB,aAAa,CACfhC,SAAU8B,EAAIG,aACdC,UAAU,EACVL,QAAS,KACPb,EAAAA,MAAImB,cACJnB,EAAAA,MAAYM,MAAA,MAAA,+CAAA,UAEZc,YAAW,KACTpB,EAAAA,MAAIqB,UAAU,CACZZ,MAAO,cACPa,KAAM,OACNC,SAAU,QAEX,MAELnB,KAAOC,IACLL,EAAAA,MAAImB,cACJnB,EAAAA,MAAcM,MAAA,QAAA,+CAAA,UAAWD,GACzB3B,EAAMmB,KAAK,cAIfG,EAAAA,MAAImB,cACJzC,EAAMmB,KAAK,YAGfO,KAAOC,IACLL,EAAAA,MAAImB,cACJnB,EAAAA,MAAcM,MAAA,QAAA,+CAAA,UAAWD,GACzB3B,EAAMmB,KAAK,cAiFXE,EAAmByB,IACL,CAChBC,MAAS,KACTC,MAAS,KACTC,SAAY,MAEGH,IAAS,OAItBb,EAAe,KAGnBJ,EAAgBhB,EAAYJ,QAUxByC,EAAa,KACjBC,cAAY,CACVpB,MAAO,OACPqB,QAAS,WAAWnD,EAAMW,sBAC1ByC,YAAa,KACbC,WAAY,OACXC,MAAYnB,IACTA,EAAIoB,SAEN5D,EAAK,SAAU,CACbU,SAAUL,EAAMK,SAChBM,SAAUX,EAAMW,cAGnB6C,OAAeC,IAChBpC,EAAAA,MAAAM,MAAA,QAAA,+CAAc,WAAY8B,cA8FjBC,EAAA,CACXzC,WACAgC,aACAU,WA5FiBC,MAAOC,EAAO,GAAIC,EAAU,CAAA,KACzC,IAEF,MAAMC,EAAiB,CACrBC,aAAa,EACbC,kBAAkB,EAClBpC,aAAa,EACbqC,YAAa,SACbC,YAAa,UACVL,GAIL,GAAIC,EAAeE,iBAAkB,CAC7B,MACAG,EAAgB,GASlB,GAVmB,CAAC,WAAY,YAGrBC,SAAiBC,IAC9B,MAAM9D,EAAQqD,EAAKS,IAAUtE,EAAMsE,KAC9B9D,GAA2B,iBAAVA,GAAuC,KAAjBA,EAAM+D,SAChDH,EAAcI,KAAe,aAAVF,EAAuB,OAAS,WAInDF,EAAcK,OAAS,EAEzB,OADA1E,EAAMmB,KAAK,OAAOkD,EAAcM,KAAK,QAC9B,CAACxC,SAAS,EAAOyC,QAAS,UAEpC,CAGD,GAAIZ,EAAeC,YAAa,CAQ1B,WAPwBd,cAAY,CACtCpB,MAAO,OACPqB,QAAS,gBACTC,YAAa,KACbC,WAAY,QAGKE,QACjB,MAAO,CAACrB,SAAS,EAAOyC,QAAS,SAEpC,CAGGZ,EAAelC,aACjBR,EAAAA,MAAIQ,YAAY,CACdC,MAAOiC,EAAeG,YACtBnC,MAAM,IAKV,MAAM6C,EAAW,CACfjE,SAAUkD,EAAKlD,UAAYX,EAAMW,SACjCN,SAAUwD,EAAKxD,UAAYL,EAAMK,SACjCS,UAAW+C,EAAK/C,WAAad,EAAMc,aAChC+C,GAmBL,OAfAlE,EAAK,OAAQ,CACXkE,KAAMe,EACNd,QAASC,IAIPA,EAAelC,aACjBR,EAAAA,MAAImB,cAIFuB,EAAeI,aACXpE,EAAAmB,KAAK6C,EAAeI,aAGrB,CAACjC,SAAS,EAAM2B,KAAMe,EAC9B,OAAQnB,GAOP,OALApC,EAAAA,MAAImB,cAEJnB,EAAAA,MAAAM,MAAA,QAAA,+CAAc,UAAW8B,GACzB1D,EAAMmB,KAAK,YAEJ,CAACgB,SAAS,EAAOyC,QAASlB,EAAMkB,SAAW,OACnD,iTCjZHE,GAAGC,gBAAgBC"}