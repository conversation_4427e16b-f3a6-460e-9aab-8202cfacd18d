"use strict";const t=require("../../../../common/vendor.js"),s={name:"uniPopup",components:{},emits:["change","maskClick"],props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},isMaskClick:{type:Boolean,default:null},maskClick:{type:Boolean,default:null},backgroundColor:{type:String,default:"none"},safeArea:{type:Boolean,default:!0},maskBackgroundColor:{type:String,default:"rgba(0, 0, 0, 0.4)"},borderRadius:{type:String}},watch:{type:{handler:function(t){this.config[t]&&this[this.config[t]](!0)},immediate:!0},isDesktop:{handler:function(t){this.config[t]&&this[this.config[this.type]](!0)},immediate:!0},maskClick:{handler:function(t){this.mkclick=t},immediate:!0},isMaskClick:{handler:function(t){this.mkclick=t},immediate:!0},showPopup(t){}},data(){return{duration:300,ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},maskClass:{position:"fixed",bottom:0,top:0,left:0,right:0,backgroundColor:"rgba(0, 0, 0, 0.4)"},transClass:{backgroundColor:"transparent",borderRadius:this.borderRadius||"0",position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupstyle:"top"}},computed:{getStyles(){let t={backgroundColor:this.bg};return this.borderRadius,t=Object.assign(t,{borderRadius:this.borderRadius}),t},isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.backgroundColor||"none"===this.backgroundColor?"transparent":this.backgroundColor}},mounted(){(()=>{const{windowWidth:s,windowHeight:i,windowTop:o,safeArea:e,screenHeight:a,safeAreaInsets:n}=t.index.getWindowInfo();this.popupWidth=s,this.popupHeight=i+(o||0),e&&this.safeArea?this.safeAreaInsets=a-e.bottom:this.safeAreaInsets=0})()},unmounted(){this.setH5Visible()},activated(){this.setH5Visible(!this.showPopup)},deactivated(){this.setH5Visible(!0)},created(){null===this.isMaskClick&&null===this.maskClick?this.mkclick=!0:this.mkclick=null!==this.isMaskClick?this.isMaskClick:this.maskClick,this.animation?this.duration=300:this.duration=0,this.messageChild=null,this.clearPropagation=!1,this.maskClass.backgroundColor=this.maskBackgroundColor},methods:{setH5Visible(t=!0){},closeMask(){this.maskShow=!1},disableMask(){this.mkclick=!1},clear(t){t.stopPropagation(),this.clearPropagation=!0},open(s){if(this.showPopup)return;s&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(s)||(s=this.type),this.config[s]?(this[this.config[s]](),this.$emit("change",{show:!0,type:s})):t.index.__f__("error","at uni_modules/uni-popup/components/uni-popup/uni-popup.vue:310","缺少类型：",s)},close(t){this.showTrans=!1,this.$emit("change",{show:!1,type:this.type}),clearTimeout(this.timer),this.timer=setTimeout((()=>{this.showPopup=!1}),300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.mkclick&&this.close())},top(t){this.popupstyle=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transClass={position:"fixed",left:0,right:0,backgroundColor:this.bg,borderRadius:this.borderRadius||"0"},t||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((()=>{this.showPoptrans(),this.messageChild&&"message"===this.type&&this.messageChild.timerClose()})))},bottom(t){this.popupstyle="bottom",this.ani=["slide-bottom"],this.transClass={position:"fixed",left:0,right:0,bottom:0,paddingBottom:this.safeAreaInsets+"px",backgroundColor:this.bg,borderRadius:this.borderRadius||"0"},t||this.showPoptrans()},center(t){this.popupstyle="center",this.ani=["fade"],this.transClass={position:"fixed",display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center",borderRadius:this.borderRadius||"0"},t||this.showPoptrans()},left(t){this.popupstyle="left",this.ani=["slide-left"],this.transClass={position:"fixed",left:0,bottom:0,top:0,backgroundColor:this.bg,borderRadius:this.borderRadius||"0",display:"flex",flexDirection:"column"},t||this.showPoptrans()},right(t){this.popupstyle="right",this.ani=["slide-right"],this.transClass={position:"fixed",bottom:0,right:0,top:0,backgroundColor:this.bg,borderRadius:this.borderRadius||"0",display:"flex",flexDirection:"column"},t||this.showPoptrans()},showPoptrans(){this.$nextTick((()=>{this.showPopup=!0,this.showTrans=!0}))}}};if(!Array){t.resolveComponent("uni-transition")()}Math;const i=t._export_sfc(s,[["render",function(s,i,o,e,a,n){return t.e$1({a:a.showPopup},a.showPopup?t.e$1({b:a.maskShow},a.maskShow?{c:t.o(n.onTap),d:t.p({name:"mask","mode-class":"fade",styles:a.maskClass,duration:a.duration,show:a.showTrans})}:{},{e:t.s(n.getStyles),f:t.n(a.popupstyle),g:t.o(((...t)=>n.clear&&n.clear(...t))),h:t.o(n.onTap),i:t.p({"mode-class":a.ani,name:"content",styles:a.transClass,duration:a.duration,show:a.showTrans}),j:t.o(((...t)=>n.touchstart&&n.touchstart(...t))),k:t.n(a.popupstyle),l:t.n(n.isDesktop?"fixforpc-z-index":""),m:t.gei(s,"")}):{})}]]);wx.createComponent(i);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-popup/components/uni-popup/uni-popup.js.map
