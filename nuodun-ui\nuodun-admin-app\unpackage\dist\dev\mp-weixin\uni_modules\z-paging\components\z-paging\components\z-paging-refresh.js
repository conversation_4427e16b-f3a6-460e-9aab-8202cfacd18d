"use strict";const e=require("../js/z-paging-static.js"),t=require("../js/z-paging-utils.js"),i=require("../js/z-paging-enum.js"),s=require("../../../../../common/vendor.js"),r={name:"z-paging-refresh",data:()=>({R:i.Enum.Refresher,refresherTimeText:"",zTheme:{title:{white:"#efefef",black:"#555555"},arrow:{white:e.zStatic.base64ArrowWhite,black:e.zStatic.base64Arrow},flower:{white:e.zStatic.base64FlowerWhite,black:e.zStatic.base64Flower},success:{white:e.zStatic.base64SuccessWhite,black:e.zStatic.base64Success},indicator:{white:"#eeeeee",black:"#777777"}}}),props:["status","defaultThemeStyle","defaultText","pullingText","refreshingText","completeText","goF2Text","defaultImg","pullingImg","refreshingImg","completeImg","refreshingAnimated","showUpdateTime","updateTimeKey","imgStyle","titleStyle","updateTimeStyle","updateTimeTextMap","unit","isIos"],computed:{ts(){return this.defaultThemeStyle},statusTextMap(){this.updateTime();const{R:e,defaultText:t,pullingText:i,refreshingText:s,completeText:r,goF2Text:a}=this;return{[e.Default]:t,[e.ReleaseToRefresh]:i,[e.Loading]:s,[e.Complete]:r,[e.GoF2]:a}},currentTitle(){return this.statusTextMap[this.status]||this.defaultText},leftImageClass(){const e=`zp-r-left-image-pre-size-${this.unit}`;return this.status===this.R.Complete?e:`zp-r-left-image ${e} ${this.status===this.R.Default?"zp-r-arrow-down":"zp-r-arrow-top"}`},leftImageStyle(){const e=this.showUpdateTime,i=e?t.u.addUnit(36,this.unit):t.u.addUnit(34,this.unit);return{width:i,height:i,"margin-right":e?t.u.addUnit(20,this.unit):t.u.addUnit(9,this.unit)}},leftImageSrc(){const e=this.R,t=this.status;return t===e.Default?this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.ReleaseToRefresh?this.pullingImg?this.pullingImg:this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.Loading?this.refreshingImg?this.refreshingImg:this.zTheme.flower[this.ts]:t===e.Complete?this.completeImg?this.completeImg:this.zTheme.success[this.ts]:t===e.GoF2?this.zTheme.arrow[this.ts]:""},rightTextStyle(){let e={};return e.color=this.zTheme.title[this.ts],e["font-size"]=t.u.addUnit(30,this.unit),e}},methods:{addUnit:(e,i)=>t.u.addUnit(e,i),updateTime(){this.showUpdateTime&&(this.refresherTimeText=t.u.getRefesrherFormatTimeByKey(this.updateTimeKey,this.updateTimeTextMap))}}};const a=s._export_sfc(r,[["render",function(e,t,i,r,a,h){return s.e$1({a:i.status!==a.R.Loading},i.status!==a.R.Loading?{b:s.n(h.leftImageClass),c:s.s(h.leftImageStyle),d:s.s(i.imgStyle),e:h.leftImageSrc}:{f:i.refreshingAnimated?1:"",g:"rpx"===i.unit?1:"",h:"px"===i.unit?1:"",i:s.s(h.leftImageStyle),j:s.s(i.imgStyle),k:h.leftImageSrc},{l:s.t(h.currentTitle),m:s.s(h.rightTextStyle),n:s.s(i.titleStyle),o:i.showUpdateTime&&a.refresherTimeText.length},i.showUpdateTime&&a.refresherTimeText.length?{p:s.t(a.refresherTimeText),q:"rpx"===i.unit?1:"",r:"px"===i.unit?1:"",s:s.s({color:a.zTheme.title[h.ts]}),t:s.s(i.updateTimeStyle)}:{},{v:s.n(i.showUpdateTime?"zp-r-container zp-r-container-padding":"zp-r-container"),w:s.gei(e,"")})}],["__scopeId","data-v-00a16504"]]);wx.createComponent(a);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/components/z-paging-refresh.js.map
