"use strict";const e=require("../../../../../common/vendor.js"),t=require("../../../../../api/home/<USER>"),n=require("../../../../../config/environment.js");if(!Array){e.resolveComponent("uni-load-more")()}const a=()=>"../../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";Math||(e.unref(o)+a+r+i)();const o=()=>"../../../../../components/custom-nav/custom-nav.js",r=()=>"../../../../../components/document-preview/index.js",i=()=>"../../../../../components/school-application-info/index.js",s={__name:"agreementHistory",setup(a){const{proxy:o}=e.getCurrentInstance(),r=o.toast,i=n.environment.fileUrl||"",s=e.ref(!0),c={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},u=e.ref([]),m=e.reactive({}),l=e=>{switch(e){case"0":return"未确认";case"1":return"待确认";case"2":return"已确认定校";case"3":return"已退回定校";case"4":return"协议被驳回";default:return"未知状态"}},f=e=>{switch(e){case"0":case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},d=t=>{if(t&&t.planList)try{return t.planList||[]}catch(n){return e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementHistory.vue:176","解析确认JSON失败",n),[]}return[]};return e.onLoad((n=>{n.orderId?(async n=>{if(!n)return r.show("参数错误"),void setTimeout((()=>{e.index.navigateBack()}),1500);s.value=!0;try{const e=await t.listAgreementSchool(n);e.data&&e.data.length>0?u.value=e.data.sort(((e,t)=>new Date(t.createTime)-new Date(e.createTime))):u.value=[]}catch(a){e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementHistory.vue:122","获取历史记录失败",a),r.show("获取历史记录失败")}finally{s.value=!1}})(n.orderId):(r.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1500))})),(t,n)=>e.e$1({a:e.p({title:"定校历史记录",isBack:!0,showLeft:!0,path:"/"}),b:s.value},s.value?{c:e.p({status:"loading",contentText:c})}:e.e$1({d:u.value&&u.value.length>0},u.value&&u.value.length>0?{e:e.f(u.value,((n,a,o)=>e.e$1({a:e.t(u.value.length-a),b:e.t(l(n.confirmationStatus)),c:e.n(f(n.confirmationStatus)),d:e.t(t.parseTime(n.confirmationConfirmTime)),e:0===a},{},{f:e.t(m[a]?"▲":"▼"),g:e.o((e=>(e=>{m[e]=!m[e]})(a)),a),h:m[a]},m[a]?e.e$1({i:n.confirmationFilePath},n.confirmationFilePath?e.e$1({j:n.confirmationFilePath},n.confirmationFilePath?{k:"778aa8a5-2-"+o,l:e.p({"file-path":e.unref(i)+n.confirmationFilePath,"file-name":"定校协议","allow-download":!0})}:{}):{},{m:"778aa8a5-3-"+o,n:e.p({schoolList:d(n)}),o:"3"===n.confirmationStatus&&n.confirmationRejectRemark},"3"===n.confirmationStatus&&n.confirmationRejectRemark?{p:e.t(n.confirmationRejectRemark)}:{},{q:"4"===n.confirmationStatus&&n.confirmationRebutRemark},"4"===n.confirmationStatus&&n.confirmationRebutRemark?{r:e.t(n.confirmationRebutRemark)}:{}):{},{s:a})))}:{}),{f:e.gei(t,"")})}},c=e._export_sfc(s,[["__scopeId","data-v-778aa8a5"]]);wx.createPage(c);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/school/agreementHistory.js.map
