"use strict";const e=require("../../../../../common/vendor.js"),a=require("../../../../../api/home/<USER>"),t=require("../../../../../common/request.js"),o=require("../../../../../common/modal.js"),u=require("../../../../../config/environment.js");if(!Array){e.resolveComponent("uni-load-more")()}const l=()=>"../../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";Math||(e.unref(r)+l)();const r=()=>"../../../../../components/custom-nav/custom-nav.js",n={__name:"installmentProof",setup(l){const{proxy:r}=e.getCurrentInstance(),n=r.toast,s=u.environment.fileUrl||"",i=e.ref(null),v=e.ref(!1),d=e.ref(null),f=e.ref(null),c=e.ref(""),m=e.ref(""),p=e.ref(!1),h=e.ref(!1),g={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},P=e.computed((()=>{if(!d.value||!f.value)return!1;const e=new Date,a=d.value.dueDate?new Date(d.value.dueDate):null;return!(a&&e<a)&&("1"===f.value.confirmStatus&&"W"===d.value.orderStatus&&("N"===d.value.isProofUploaded||"R"===d.value.proofVerifyStatus))})),w=e.computed((()=>{if(!d.value||!d.value.dueDate)return!1;const e=new Date,a=new Date(d.value.dueDate);return e.setHours(0,0,0,0),a.setHours(0,0,0,0),e<a})),y=e.computed((()=>{if(!d.value||!d.value.dueDate)return 0;const e=new Date,a=new Date(d.value.dueDate);e.setHours(0,0,0,0),a.setHours(0,0,0,0);const t=a.getTime()-e.getTime();return Math.ceil(t/864e5)})),_=async()=>{if(i.value){v.value=!0;try{const e=await a.getProof(i.value);200===e.code&&e.data?(e.data.installment&&(d.value=e.data.installment,c.value="",m.value="","Y"===d.value.isProofUploaded&&d.value.proofFilePath&&(m.value=d.value.proofFilePath)),e.data.agreement&&(f.value=e.data.agreement)):n.show("获取分期详情失败")}catch(t){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/installmentProof.vue:218","获取分期详情失败",t),n.show("获取分期详情失败")}finally{v.value=!1}}},D=(e,a)=>f.value&&"1"!==f.value.confirmStatus||"N"===d.value.isProofUploaded?"status-pending":"Y"===d.value.isProofUploaded&&"S"===a?"status-success":"Y"===d.value.isProofUploaded&&"R"===a?"status-rejected":"Y"===d.value.isProofUploaded&&"W"===a?"status-pending":"",x=()=>{w.value?n.show(`还未到付款时间，请在${parseTime(d.value.dueDate,"{y}-{m}-{d}")}当天或之后再提交`):p.value||e.index.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:e=>{e.tempFilePaths&&e.tempFilePaths.length>0&&(c.value=e.tempFilePaths[0],m.value="",S())}})},S=async()=>{if(c.value){p.value=!0;try{const a=await(a=>new Promise(((o,u)=>{e.index.showLoading("上传中..."),t.upload({filePath:a,name:"file",formData:{fileBizType:"installment_payment_proof"},success:e=>{e&&200===e.code&&e.data?o(e.data.filePath):u(new Error(e&&e.msg?e.msg:"上传失败"))},fail:a=>{e.index.__f__("error","at pages/mine/agreements/stInfo/contract/installmentProof.vue:330","上传请求失败:",a),u(new Error("上传失败"))},complete:()=>{e.index.hideLoading()}})})))(c.value);m.value=a,n.show("凭证图片上传成功")}catch(a){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/installmentProof.vue:350","上传凭证图片失败:",a),c.value="",n.show("凭证图片上传失败")}finally{p.value=!1}}},T=async()=>{m.value?w.value?n.show(`还未到付款时间，请在${parseTime(d.value.dueDate,"{y}-{m}-{d}")}当天或之后再提交`):o.createModal({title:"确认提交凭证",content:"确定要提交付款凭证吗？\n提交后需等待审核确认，审核通过后该期付款将被标记为已完成。",confirmText:"确认提交",cancelText:"取消"}).then((async t=>{if(t.confirm){h.value=!0,e.index.showLoading("提交中...");try{200===(await a.uploadInstallmentProof({id:i.value,proofFilePath:m.value,isProofUploaded:"Y",proofVerifyStatus:"W",orderStatus:"W"})).code&&(n.show("凭证提交成功，等待审核确认"),c.value="",m.value="",setTimeout((()=>{_()}),500))}catch(o){e.index.__f__("error","at pages/mine/agreements/stInfo/contract/installmentProof.vue:408","提交凭证失败:",o)}finally{h.value=!1,e.index.hideLoading()}}})):n.show("请先上传凭证图片")};return e.onLoad((a=>{a.id?(i.value=a.id,_()):(n.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1500))})),(a,t)=>{return e.e$1({a:e.p({title:"分期凭证",showLeft:!0}),b:v.value},v.value?{c:e.p({status:"loading","content-text":g})}:e.e$1({d:d.value},d.value?e.e$1({e:e.t(d.value.contractCode||"暂无"),f:e.t(d.value.dueAmount||0),g:e.t(a.parseTime(d.value.dueDate,"{y}-{m}-{d}")||"暂无"),h:e.t((d.value.orderStatus,o=d.value.proofVerifyStatus,f.value&&"1"!==f.value.confirmStatus?"等待协议确认":"N"===d.value.isProofUploaded?"去上传凭证":"Y"===d.value.isProofUploaded&&"S"===o?"凭证已审核通过":"Y"===d.value.isProofUploaded&&"R"===o?"凭证审核失败":"Y"===d.value.isProofUploaded&&"W"===o?"凭证审核中":"未知状态")),i:e.n(D(d.value.orderStatus,d.value.proofVerifyStatus)),j:d.value.statusChangeDesc&&"R"===d.value.proofVerifyStatus},d.value.statusChangeDesc&&"R"===d.value.proofVerifyStatus?{k:e.t(d.value.statusChangeDesc)}:{}):{},{l:d.value&&d.value.proofFilePath},d.value&&d.value.proofFilePath?{m:e.unref(s)+d.value.proofFilePath,n:e.o((a=>{var t;(t=e.unref(s)+d.value.proofFilePath)&&e.index.previewImage({urls:[t],current:t})})),o:e.t(a.parseTime(d.value.proofUploadTime))}:{},{p:P.value},P.value?e.e$1({q:e.t(d.value&&"Y"===d.value.isProofUploaded?"重新上传凭证":"上传凭证"),r:c.value},c.value?{s:c.value}:{},{t:e.o(x),v:p.value},(p.value,{}),{w:m.value&&!p.value},(m.value&&p.value,{})):{},{x:w.value},w.value?{y:e.t(y.value),z:e.t(a.parseTime(d.value.dueDate,"{y}-{m}-{d}"))}:{},{A:P.value&&m.value},P.value&&m.value?{B:e.t(h.value?"提交中...":"提交凭证"),C:e.o(T),D:h.value}:{}),{E:e.gei(a,"")});var o}}},s=e._export_sfc(n,[["__scopeId","data-v-5c1a1772"]]);wx.createPage(s);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/contract/installmentProof.js.map
