"use strict";const e=require("./Func.js"),r=require("./DictConverter.js"),t={metas:{"*":{request:e=>Promise.resolve([]),responseConverter:function(e,t){const n=e.content instanceof Array?e.content:e;if(void 0===n)return[];return n.map((e=>r.dictConverter(e,t)))},labelField:"label",valueField:"value"}},DEFAULT_LABEL_FIELDS:["label","name","title"],DEFAULT_VALUE_FIELDS:["value","id","uid","key"]};exports.mergeOptions=function(r){e.mergeRecursive(t,r)},exports.options=t;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/dict/DictOptions.js.map
