"use strict";const s=require("./vendor.js"),e=(e,r="default",o=2e3,n={})=>{s.index.showToast({title:e,icon:"success"===r?"success":"error"===r?"error":"none",duration:o,mask:!1})},r={show:e,success:(s,r,o)=>e(s,"success",r,o),error:(s,r,o)=>e(s,"error",r,o),warning:(s,r,o)=>e(s,"warning",r,o),info:(s,r,o)=>e(s,"info",r,o),longText:(e,r="default",o={})=>{s.index.showToast({title:e,icon:"success"===r?"success":"error"===r?"error":"none",duration:3e3,mask:!1})}};exports.toast=r;
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/toast.js.map
