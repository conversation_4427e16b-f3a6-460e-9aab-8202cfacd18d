"use strict";require("./vendor.js"),require("./toast.js");exports.maskIdCard=function(e){return e?15===e.length?e.replace(/(\d{6})\d{6}(\d{3})/,"$1******$2"):18===e.length?e.replace(/(\d{6})\d{8}(\w{4})/,"$1********$2"):e:e},exports.maskPhone=function(e){return!e||e.length<7?e:e.replace(/(\d{3})\d*(\d{4})/,"$1****$2")},exports.pageRedirectConfig={STFOLLOWDETAIL:{path:"/pages/home/<USER>/detail",paramsHandler:e=>({url:`/pages/home/<USER>/detail?id=${e}`})},COMAGREEMENT:{path:"/pages/mine/agreements/chan/agreementDetail",paramsHandler:e=>({url:`/pages/mine/agreements/chan/agreementDetail?id=${e}`})}},exports.parseTime=function(e,t){if(0===arguments.length||!e||e.length<1)return null;const r=t||"{y}-{m}-{d} {h}:{i}:{s}";let n;"object"==typeof e?n=e:("string"==typeof e&&/^[0-9]+$/.test(e)?e=parseInt(e):"string"==typeof e&&(e=e.replace(new RegExp(/-/gm),"/").replace("T"," ").replace(new RegExp(/\.[\d]{3}/gm),"")),"number"==typeof e&&10===e.toString().length&&(e*=1e3),n=new Date(e));const a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()};return r.replace(/{(y|m|d|h|i|s|a)+}/g,((e,t)=>{let r=a[t];return"a"===t?["日","一","二","三","四","五","六"][r]:(e.length>0&&r<10&&(r="0"+r),r||0)}))};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/utils.js.map
