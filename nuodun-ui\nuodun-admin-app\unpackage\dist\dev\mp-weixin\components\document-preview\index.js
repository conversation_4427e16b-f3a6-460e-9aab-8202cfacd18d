"use strict";const e=require("../../common/vendor.js"),t=require("../../common/modal.js"),i=require("../../common/fileUtils.js"),o=require("../../config/environment.js"),a={__name:"index",props:{filePath:{type:String,default:""},fileName:{type:String,default:"文件"},urlPrefix:{type:String,default:""},allowView:{type:Boolean,default:!0},allowDownload:{type:Boolean,default:!1},allowDelete:{type:Boolean,default:!1}},emits:["delete","save"],setup(a,{expose:n,emit:l}){const s=l,{proxy:d}=e.getCurrentInstance(),r=d.toast,c=a,u=e.computed((()=>i.getFileType(c.filePath))),f=e.computed((()=>["image","document"].includes(u.value))),m=e.computed((()=>i.getFileTypeIcon(c.fileName))),h=e.computed((()=>c.filePath?c.filePath.startsWith("http://")||c.filePath.startsWith("https://")?c.filePath:(c.urlPrefix||o.environment.fileUrl||"")+c.filePath:"")),w=()=>{if(!c.filePath)return void r.show("文件路径为空");if(!f.value){const e=p(u.value);return void r.show(`暂不支持打开 ${e} 文件，请下载后查看`)}let t=c.filePath;if(t.startsWith("http")||(t=h.value),"image"===u.value)e.index.previewImage({urls:[t],current:t,fail:t=>{e.index.__f__("error","at components/document-preview/index.vue:124","图片预览失败:",t),r.show("图片预览失败")}});else if("document"===u.value)x(t);else{const e=p(u.value);r.show(`暂不支持打开 ${e} 文件，请下载后查看`)}},x=t=>{e.index.showLoading({title:"准备预览...",mask:!0}),e.index.downloadFile({url:t,success:t=>{200===t.statusCode?e.index.openDocument({filePath:t.tempFilePath,showMenu:!0,success:()=>{e.index.hideLoading(),e.index.__f__("log","at components/document-preview/index.vue:170","文档预览成功"),setTimeout((()=>{e.index.showToast({title:"可点击右上角保存到手机",icon:"none",duration:3e3})}),1e3)},fail:t=>{e.index.hideLoading(),e.index.__f__("error","at components/document-preview/index.vue:182","文档预览失败:",t),r.show("文档预览失败")}}):(e.index.hideLoading(),r.show("文件下载失败"))},fail:t=>{e.index.hideLoading(),e.index.__f__("error","at components/document-preview/index.vue:193","文件下载失败:",t),r.show("文件下载失败")}})},p=e=>({image:"图片",video:"视频",document:"文档"}[e]||"此类型"),v=()=>{x(h.value)},g=()=>{t.createModal({title:"确认删除",content:`确定要删除文件"${c.fileName}"吗？删除后将无法恢复`,confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&s("delete",{filePath:c.filePath,fileName:c.fileName})})).catch((t=>{e.index.__f__("error","at components/document-preview/index.vue:313","删除确认框异常:",t)}))};return n({viewFile:w,deleteFile:g,handleSave:async(i={},o={})=>{try{const a={showConfirm:!0,validateRequired:!0,showLoading:!0,loadingText:"保存中...",successText:"保存成功",...o};if(a.validateRequired){const e=[];if(["fileName","filePath"].forEach((t=>{const o=i[t]||c[t];(!o||"string"==typeof o&&""===o.trim())&&e.push("fileName"===t?"文件名称":"文件路径")})),e.length>0)return r.show(`请填写：${e.join("、")}`),{success:!1,message:"必填项校验失败"}}if(a.showConfirm){if(!(await t.createModal({title:"确认保存",content:"确定要保存当前文件信息吗？",confirmText:"确定",cancelText:"取消"})).confirm)return{success:!1,message:"用户取消操作"}}a.showLoading&&e.index.showLoading({title:a.loadingText,mask:!0});const n={fileName:i.fileName||c.fileName,filePath:i.filePath||c.filePath,urlPrefix:i.urlPrefix||c.urlPrefix,...i};return s("save",{data:n,options:a}),a.showLoading&&e.index.hideLoading(),a.successText&&r.show(a.successText),{success:!0,data:n}}catch(a){return e.index.hideLoading(),e.index.__f__("error","at components/document-preview/index.vue:399","保存校验异常:",a),r.show("保存失败，请重试"),{success:!1,message:a.message||"保存异常"}}}}),(t,i)=>e.e$1({a:e.t(m.value),b:e.t(a.fileName),c:!f.value},(f.value,{}),{d:a.allowView&&f.value},a.allowView&&f.value?{e:e.o(w)}:{},{f:a.allowDownload},a.allowDownload?{g:e.o(v)}:{},{h:a.allowDelete},a.allowDelete?{i:e.o(g)}:{},{j:e.gei(t,"")})}},n=e._export_sfc(a,[["__scopeId","data-v-d030b7b6"]]);wx.createComponent(n);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/document-preview/index.js.map
