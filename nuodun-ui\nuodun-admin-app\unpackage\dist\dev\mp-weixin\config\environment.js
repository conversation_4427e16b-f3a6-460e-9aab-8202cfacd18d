"use strict";const e={development:{baseUrl:"http://***************/nuodun-joolun-app",fileUrl:"https://nuodun-test.oss-cn-beijing.aliyuncs.com",appId:"wxaf5189b0c64f5030",wxAppId:"wxa719cd45e662df06",aesKey:"iQP2R3pY4ATCKW0D",enableRequestEncrypt:!1},production:{baseUrl:"http://www.ndjykj.com/nuodun-joolun-app",fileUrl:"https://nuodun-po.oss-cn-shenzhen.aliyuncs.com",appId:"wx52c99a7115d1314a",wxAppId:"wxa719cd45e662df06",aesKey:"iQP2R3pY4ATCKW0D",enableRequestEncrypt:!0}},n=()=>(()=>{if("undefined"!=typeof window){const e=window.location.hostname;return"localhost"===e||"127.0.0.1"===e||e.startsWith("192.168")}return!0})()?e.development:e.production,t=()=>n().wxAppId,p={name:"诺盾教育",development:e.development,production:e.production,get baseUrl(){return n().baseUrl},get fileUrl(){return n().fileUrl},get appId(){return n().appId},get wxAppId(){return t()},get currentAppId(){return t()},get aesKey(){return n().aesKey},get enableRequestEncrypt(){return n().enableRequestEncrypt}};exports.environment=p;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/environment.js.map
