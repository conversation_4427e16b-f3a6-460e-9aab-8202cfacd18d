"use strict";const i=require("../../../../common/vendor.js"),t={name:"u-icon",beforeCreate(){i.index.loadFontFace({family:"uicon-iconfont",source:'url("'+i.config.iconUrl+'")',success(){},fail(){i.index.__f__("error","at node_modules/uview-plus/components/u-icon/u-icon.vue:98","内置字体图标加载出错")}}),i.config.customIcon.family&&i.index.loadFontFace({family:i.config.customIcon.family,source:'url("'+i.config.customIcon.url+'")',success(){},fail(){i.index.__f__("error","at node_modules/uview-plus/components/u-icon/u-icon.vue:109","扩展字体图标加载出错")}})},data:()=>({}),emits:["click"],mixins:[i.mpMixin,i.mixin,i.props],computed:{uClasses(){let t=[];return t.push(this.customPrefix+"-"+this.name),"uicon"==this.customPrefix?t.push("u-iconfont"):t.push(this.customPrefix),this.color&&i.config.type.includes(this.color)&&t.push("u-icon__icon--"+this.color),t},iconStyle(){let t={};return t={fontSize:i.addUnit(this.size),lineHeight:i.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:i.addUnit(this.top)},"uicon"!==this.customPrefix&&(t.fontFamily=this.customPrefix),this.color&&!i.config.type.includes(this.color)&&(t.color=this.color),t},isImg(){return-1!==this.name.indexOf("/")},imgStyle(){let t={};return t.width=this.width?i.addUnit(this.width):i.addUnit(this.size),t.height=this.height?i.addUnit(this.height):i.addUnit(this.size),t},icon(){return"uicon"!==this.customPrefix?i.config.customIcons[this.name]||this.name:i.icons["uicon-"+this.name]||this.name}},methods:{addStyle:i.addStyle,addUnit:i.addUnit,clickHandler(i){this.$emit("click",this.index,i),this.stop&&this.preventEvent(i)}}};const e=i._export_sfc(t,[["render",function(t,e,o,s,n,c){return i.e$1({a:c.isImg},c.isImg?{b:t.name,c:t.imgMode,d:i.s(c.imgStyle),e:i.s(c.addStyle(t.customStyle))}:{f:i.t(c.icon),g:i.n(c.uClasses),h:i.s(c.iconStyle),i:i.s(c.addStyle(t.customStyle)),j:t.hoverClass},{k:""!==t.label},""!==t.label?{l:i.t(t.label),m:t.labelColor,n:c.addUnit(t.labelSize),o:"right"==t.labelPos?c.addUnit(t.space):0,p:"bottom"==t.labelPos?c.addUnit(t.space):0,q:"left"==t.labelPos?c.addUnit(t.space):0,r:"top"==t.labelPos?c.addUnit(t.space):0}:{},{s:i.o(((...i)=>c.clickHandler&&c.clickHandler(...i))),t:i.n("u-icon--"+t.labelPos),v:i.gei(t,"")})}],["__scopeId","data-v-1c933a9a"]]);wx.createComponent(e);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/node-modules/uview-plus/components/u-icon/u-icon.js.map
