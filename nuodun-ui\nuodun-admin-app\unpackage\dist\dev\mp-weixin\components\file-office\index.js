"use strict";const e=require("../../common/vendor.js"),i={__name:"index",props:{filePath:{type:String,default:""},fileType:{type:String,default:""},autoLoad:{type:Boolean,default:!0},urlPrefix:{type:String,default:"/office-file"}},emits:["rendered","error"],setup(i,{expose:o,emit:a}){const{proxy:l}=e.getCurrentInstance(),n=l.toast,t=i,d=e.ref(!1),u=e.ref(""),s=e.ref(!1),f={image:["jpg","jpeg","png","gif","bmp","webp","svg"],excel:["xls","xlsx","csv"],word:["doc","docx","rtf"],pdf:["pdf"]},r=e.computed((()=>t.urlPrefix+t.filePath)),p=e.computed((()=>{var e;return t.filePath&&(null==(e=t.filePath.split(".").pop())?void 0:e.toLowerCase())||""})),c=e.computed((()=>t.fileType?"IMAGE"===t.fileType.toUpperCase():f.image.includes(p.value))),v=e.computed((()=>t.fileType?"EXCEL"===t.fileType.toUpperCase():f.excel.includes(p.value))),x=e.computed((()=>t.fileType?"WORD"===t.fileType.toUpperCase():f.word.includes(p.value))),h=e.computed((()=>t.fileType?"PDF"===t.fileType.toUpperCase():f.pdf.includes(p.value))),m=e.computed((()=>["pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(p.value))),g=()=>h.value?"PDF文档":x.value?"Word文档":v.value?"Excel表格":c.value?"图片文件":"文档",w=()=>{d.value=!0,s.value=!1,u.value=r.value,c.value,d.value=!1};e.watch((()=>t.filePath),(e=>{e&&t.autoLoad&&w()}),{immediate:!0}),e.onMounted((()=>{t.filePath&&t.autoLoad&&w()}));const _=()=>{if(!t.filePath)return"未知文件";return t.filePath.split("/").pop()||"未知文件"},y=()=>h.value?"file-icon-pdf":x.value?"file-icon-word":v.value?"file-icon-excel":"file-icon-default",P=()=>"微信小程序暂不支持预览此文件类型，请下载后查看",L=()=>{c.value&&u.value&&e.index.previewImage({urls:[u.value],current:u.value,fail:i=>{e.index.__f__("error","at components/file-office/index.vue:284","图片预览失败:",i),n.show("图片预览失败")}})},C=()=>{m.value?(e.index.showLoading({title:"准备预览...",mask:!0}),e.index.downloadFile({url:u.value,success:i=>{200===i.statusCode?e.index.openDocument({filePath:i.tempFilePath,showMenu:!0,success:()=>{e.index.hideLoading(),e.index.__f__("log","at components/file-office/index.vue:313","文档预览成功")},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at components/file-office/index.vue:317","文档预览失败:",i),n.show("文档预览失败，请尝试下载"),F()}}):(e.index.hideLoading(),n.show("文件下载失败"))},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at components/file-office/index.vue:330","文件下载失败:",i),n.show("文件下载失败")}})):n.show("当前文件类型不支持预览")},F=()=>{u.value?(e.index.showLoading({title:"准备预览...",mask:!0}),e.index.downloadFile({url:u.value,success:i=>{200===i.statusCode?e.index.openDocument({filePath:i.tempFilePath,showMenu:!0,success:()=>{e.index.hideLoading(),setTimeout((()=>{e.index.showToast({title:"可点击右上角保存到手机",icon:"none",duration:3e3})}),1e3)},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at components/file-office/index.vue:393","文档预览失败:",i),n.show("文档预览失败")}}):(e.index.hideLoading(),n.show("文件下载失败"))},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at components/file-office/index.vue:404","文件下载失败:",i),n.show("文件下载失败")}})):n.show("没有可下载的文件")},T=()=>{e.index.setClipboardData({data:u.value,success:()=>{n.show("链接已复制，可在浏览器中打开下载")},fail:()=>{n.show("复制失败")}})};return o({downloadFile:F,previewInMiniProgram:C,previewImage:L,getFileName:_,getFileIconClass:y,getUnsupportedMessage:P,shareFile:()=>{e.index.share({provider:"weixin",scene:"WXSceneSession",type:0,href:u.value,title:`分享文件：${_()}`,summary:"点击查看文件",success:()=>{n.show("分享成功")},fail:i=>{e.index.__f__("error","at components/file-office/index.vue:505","分享失败:",i),T()}})},copyFileLink:T}),(i,o)=>e.e$1({a:u.value&&c.value},u.value&&c.value?{b:u.value,c:e.o(L)}:u.value&&m.value?{e:e.t(g()),f:e.n(y()),g:e.t(_()),h:e.o(C),i:e.o(F)}:{j:e.t(g()),k:e.t("微信小程序暂不支持预览此文件类型，请下载后查看"),l:e.o(F)},{d:u.value&&m.value,m:e.gei(i,"")})}};wx.createComponent(i);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/file-office/index.js.map
