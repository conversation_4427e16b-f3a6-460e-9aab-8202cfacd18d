"use strict";const e=require("../../common/vendor.js");if(!Array){e.resolveComponent("uni-icons")()}Math;const t={__name:"custom-nav",props:{title:{type:String,default:""},showLeft:{type:Boolean,default:!1},showRight:{type:Boolean,default:!1},showTitle:{type:Boolean,default:!0},transparent:{type:Boolean,default:!1},hidden:{type:Boolean,default:!1},path:{type:String,default:"/"}},setup(t){const o=t,a=e.ref(20),n=e.computed((()=>a.value+44));e.onMounted((()=>{const t=e.index.getSystemInfoSync();a.value=t.statusBarHeight}));const s=()=>{getCurrentPages().length>1?e.index.navigateBack({delta:1}):o.path&&e.index.redirectTo({url:o.path})};return(o,i)=>e.e$1({a:t.showLeft},t.showLeft?{b:e.p({type:"back",size:"20",color:"#333333"}),c:e.o(s)}:{},{d:t.showTitle},t.showTitle?{e:e.t(t.title)}:{},{f:t.showRight},(t.showRight,{}),{g:t.transparent?1:"",h:t.hidden?1:"",i:a.value+"px",j:n.value+"px",k:e.gei(o,"")})}},o=e._export_sfc(t,[["__scopeId","data-v-1b09776d"]]);wx.createComponent(o);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/custom-nav/custom-nav.js.map
