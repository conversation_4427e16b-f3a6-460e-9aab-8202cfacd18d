<template>
  <view class="file-office-container">
    <!-- #ifdef H5 -->
    <!-- PDF文件预览 -->
    <VueOfficePdf v-if="fileUrl && isPdfFile" :src="fileUrl" class="office-viewer" @rendered="handleRendered"
      @error="handleError">
    </VueOfficePdf>

    <!-- Word文件预览 -->
    <VueOfficeDocx v-else-if="fileUrl && isWordFile" :src="fileUrl" class="office-viewer" @rendered="handleRendered"
      @error="handleError">
    </VueOfficeDocx>

    <!-- Excel文件预览 -->
    <VueOfficeExcel v-else-if="fileUrl && isExcelFile" :src="fileUrl" class="office-viewer" @rendered="handleRendered"
      @error="handleError">
    </VueOfficeExcel>

    <!-- 图片预览 -->
    <image v-else-if="fileUrl && isImageFile" :src="fileUrl" mode="aspectFit" class="image-preview"></image>

    <!-- 加载中状态 -->
    <view v-else-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">文件加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text class="error-text">无法预览该文件类型</text>
    </view>
    <!-- #endif -->

    <!-- #ifndef H5 -->
    <!-- 非H5环境（小程序、APP）文件预览 -->
    <view v-if="fileUrl && isImageFile" class="image-container">
      <!-- 图片预览 -->
      <image :src="fileUrl" mode="aspectFit" class="image-preview" @tap="previewImage"></image>
    </view>
    <view v-else-if="fileUrl && canPreviewInMiniProgram" class="miniprogram-preview">
      <view class="file-info">
        <view class="file-icon" :class="getFileIconClass()">
          <text class="file-type-text">{{ getFileTypeName() }}</text>
        </view>
        <view class="file-details">
          <text class="file-name">{{ getFileName() }}</text>
          <text class="file-desc">点击预览文档</text>
        </view>
      </view>
      <view class="action-buttons">
        <button class="preview-btn" @click="previewInMiniProgram">预览文档</button>
        <button class="download-btn secondary" @click="downloadFile">下载文件</button>
      </view>
    </view>
    <view v-else class="unsupported-container">
      <view class="unsupported-icon">📄</view>
      <text class="unsupported-title">{{ getFileTypeName() }}</text>
      <text class="unsupported-desc">{{ getUnsupportedMessage() }}</text>
      <button class="download-btn" @click="downloadFile">下载文件</button>
    </view>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue';
// #ifdef H5
import VueOfficePdf from '@vue-office/pdf';
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
// #endif
const { proxy } = getCurrentInstance();
const toast = proxy.toast;

const props = defineProps({
  // 文件路径
  filePath: {
    type: String,
    default: ''
  },
  // 文件类型(PDF, WORD, EXCEL, IMAGE)
  fileType: {
    type: String,
    default: ''
  },
  // 是否自动加载
  autoLoad: {
    type: Boolean,
    default: true
  },
  // URL前缀
  urlPrefix: {
    type: String,
    default: '/office-file'
  }
});

const emit = defineEmits(['rendered', 'error']);

// 响应式数据
const loading = ref(false);
const fileUrl = ref('');
const renderError = ref(false);

// 文件类型扩展名集合
const fileExtensions = {
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
  excel: ['xls', 'xlsx', 'csv'],
  word: ['doc', 'docx', 'rtf'],
  pdf: ['pdf']
};

// 计算属性
// 完整的文件URL
const fullFileUrl = computed(() => {
  const prefix = props.urlPrefix;
  return prefix + props.filePath;
});

// 文件扩展名
const fileExtension = computed(() => {
  if (!props.filePath) return '';
  return props.filePath.split('.').pop()?.toLowerCase() || '';
});

// 是否是图片文件
const isImageFile = computed(() => {
  if (props.fileType) {
    return props.fileType.toUpperCase() === 'IMAGE';
  }
  return fileExtensions.image.includes(fileExtension.value);
});

// 是否是Excel文件
const isExcelFile = computed(() => {
  if (props.fileType) {
    return props.fileType.toUpperCase() === 'EXCEL';
  }
  return fileExtensions.excel.includes(fileExtension.value);
});

// 是否是Word文件
const isWordFile = computed(() => {
  if (props.fileType) {
    return props.fileType.toUpperCase() === 'WORD';
  }
  return fileExtensions.word.includes(fileExtension.value);
});

// 是否是PDF文件
const isPdfFile = computed(() => {
  if (props.fileType) {
    return props.fileType.toUpperCase() === 'PDF';
  }
  return fileExtensions.pdf.includes(fileExtension.value);
});

// 判断是否可以在小程序中预览
const canPreviewInMiniProgram = computed(() => {
  // #ifdef MP-WEIXIN
  // 微信小程序支持预览的文件类型
  const wechatSupportedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  return wechatSupportedTypes.includes(fileExtension.value);
  // #endif

  // #ifdef MP-ALIPAY
  // 支付宝小程序支持的文件类型
  const alipaySupportedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];
  return alipaySupportedTypes.includes(fileExtension.value);
  // #endif

  // #ifdef APP-PLUS
  // APP环境支持更多文件类型
  return isPdfFile.value || isWordFile.value || isExcelFile.value;
  // #endif

  // 其他环境不支持
  return false;
});

// 文件渲染完成回调
const handleRendered = () => {
  loading.value = false;
  emit('rendered');
};

// 文件渲染错误回调
const handleError = (error) => {
  console.error('文件预览失败:', error);
  renderError.value = true;
  loading.value = false;
  emit('error', error);
};

// 获取文件类型名称
const getFileTypeName = () => {
  if (isPdfFile.value) return 'PDF文档';
  if (isWordFile.value) return 'Word文档';
  if (isExcelFile.value) return 'Excel表格';
  if (isImageFile.value) return '图片文件';
  return '文档';
};

// 初始化文件预览
const initPreview = () => {
  loading.value = true;
  renderError.value = false;

  // 获取文件URL
  fileUrl.value = fullFileUrl.value;

  // 如果是图片文件，直接显示
  if (isImageFile.value) {
    loading.value = false;
    return;
  }

  // #ifdef H5
  // 使用@vue-office系列组件加载文件，渲染完成后会通过handleRendered回调处理
  if (isPdfFile.value || isWordFile.value || isExcelFile.value) {
    // 渲染将由组件完成，这里不需要额外处理
    return;
  }
  // #endif

  // 其他文件类型无法预览
  loading.value = false;
};

// 监听文件路径变化 - 移动到initPreview定义之后
watch(() => props.filePath, (newVal) => {
  if (newVal && props.autoLoad) {
    initPreview();
  }
}, { immediate: true });

// 页面加载完成
onMounted(() => {
  if (props.filePath && props.autoLoad) {
    initPreview();
  }
});

// 获取文件名
const getFileName = () => {
  if (!props.filePath) return '未知文件';
  const fileName = props.filePath.split('/').pop();
  return fileName || '未知文件';
};

// 获取文件图标样式类
const getFileIconClass = () => {
  if (isPdfFile.value) return 'file-icon-pdf';
  if (isWordFile.value) return 'file-icon-word';
  if (isExcelFile.value) return 'file-icon-excel';
  return 'file-icon-default';
};

// 获取不支持预览的提示信息
const getUnsupportedMessage = () => {
  // #ifdef MP-WEIXIN
  return '微信小程序暂不支持预览此文件类型，请下载后查看';
  // #endif

  // #ifdef MP-ALIPAY
  return '支付宝小程序暂不支持预览此文件类型，请下载后查看';
  // #endif

  // #ifdef APP-PLUS
  return 'APP暂不支持预览此文件类型，请下载后查看';
  // #endif

  return '当前环境不支持预览此文件类型，请下载后查看';
};

// 图片预览
const previewImage = () => {
  if (!isImageFile.value || !fileUrl.value) return;

  uni.previewImage({
    urls: [fileUrl.value],
    current: fileUrl.value,
    fail: (err) => {
      console.error('图片预览失败:', err);
      toast.show('图片预览失败');
    }
  });
};

// 小程序中预览文档
const previewInMiniProgram = () => {
  if (!canPreviewInMiniProgram.value) {
    toast.show('当前文件类型不支持预览');
    return;
  }

  uni.showLoading({
    title: '准备预览...',
    mask: true
  });

  // 下载文件并预览
  uni.downloadFile({
    url: fileUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        // 使用 openDocument 打开文档
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            uni.hideLoading();
            console.log('文档预览成功');
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('文档预览失败:', err);
            toast.show('文档预览失败，请尝试下载');
            // 预览失败时提供下载选项
            downloadFile();
          }
        });
      } else {
        uni.hideLoading();
        toast.show('文件下载失败');
      }
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('文件下载失败:', err);
      toast.show('文件下载失败');
    }
  });
};

// 下载文件
const downloadFile = () => {
  if (!fileUrl.value) {
    toast.show('没有可下载的文件')
    return;
  }

  // #ifdef H5
  // H5环境直接下载到本地
  const isWechatBrowser = /micromessenger/i.test(navigator.userAgent);

  if (isWechatBrowser) {
    // 微信浏览器中直接打开链接，让用户选择下载
    window.open(fileUrl.value, '_blank');
    toast.show('请在浏览器中选择下载');
  } else {
    // 普通浏览器使用a标签下载
    const fileName = getFileName();
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = fileUrl.value;
    a.download = fileName;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    toast.show('文件下载中...');
  }
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序环境：直接预览文件，并提示用户保存
  uni.showLoading({
    title: '准备预览...',
    mask: true
  });

  uni.downloadFile({
    url: fileUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            uni.hideLoading();
            // 预览成功后提示用户保存
            setTimeout(() => {
              uni.showToast({
                title: '可点击右上角保存到手机',
                icon: 'none',
                duration: 3000
              });
            }, 1000);
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('文档预览失败:', err);
            toast.show('文档预览失败');
          }
        });
      } else {
        uni.hideLoading();
        toast.show('文件下载失败');
      }
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('文件下载失败:', err);
      toast.show('文件下载失败');
    }
  });
  // #endif

  // #ifdef MP-ALIPAY
  // 支付宝小程序：直接下载并保存
  uni.showLoading({
    title: '下载中...',
    mask: true
  });

  uni.downloadFile({
    url: fileUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveFile({
          tempFilePath: res.tempFilePath,
          success: () => {
            uni.hideLoading();
            toast.show('文件已保存到本地');
          },
          fail: () => {
            uni.hideLoading();
            toast.show('文件保存失败');
          }
        });
      } else {
        uni.hideLoading();
        toast.show('文件下载失败');
      }
    },
    fail: () => {
      uni.hideLoading();
      toast.show('文件下载失败');
    }
  });
  // #endif

  // #ifdef APP-PLUS
  // APP环境：下载到手机存储
  uni.showLoading({
    title: '下载中...',
    mask: true
  });

  uni.downloadFile({
    url: fileUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveFile({
          tempFilePath: res.tempFilePath,
          success: (saveRes) => {
            uni.hideLoading();
            toast.show('文件已保存到手机');

            // APP中可以直接打开文件
            uni.openDocument({
              filePath: saveRes.savedFilePath,
              success: () => {
                console.log('打开文档成功');
              },
              fail: () => {
                toast.show('文件已保存，但无法预览此类型文件');
              }
            });
          },
          fail: () => {
            uni.hideLoading();
            toast.show('文件保存失败');
          }
        });
      } else {
        uni.hideLoading();
        toast.show('文件下载失败');
      }
    },
    fail: () => {
      uni.hideLoading();
      toast.show('文件下载失败');
    }
  });
  // #endif
};

// 微信小程序分享文件
const shareFile = () => {
  // #ifdef MP-WEIXIN
  // 微信小程序中可以使用分享功能
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: fileUrl.value,
    title: `分享文件：${getFileName()}`,
    summary: '点击查看文件',
    success: () => {
      toast.show('分享成功');
    },
    fail: (err) => {
      console.error('分享失败:', err);
      // 分享失败时复制链接
      copyFileLink();
    }
  });
  // #endif
};

// 复制文件链接
const copyFileLink = () => {
  uni.setClipboardData({
    data: fileUrl.value,
    success: () => {
      toast.show('链接已复制，可在浏览器中打开下载');
    },
    fail: () => {
      toast.show('复制失败');
    }
  });
};

// 导出给模板使用的公共方法
defineExpose({
  downloadFile,
  previewInMiniProgram,
  previewImage,
  getFileName,
  getFileIconClass,
  getUnsupportedMessage,
  shareFile,
  copyFileLink
});
</script>

<style lang="scss">
.file-office-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f5f5f5;
}

.office-viewer {
  width: 100%;
  height: 100%;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid var(--nuodun-primary-color, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 30rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unsupported-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60rpx 40rpx;
  text-align: center;
}

.unsupported-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.unsupported-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.unsupported-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.download-btn {
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  min-width: 200rpx;
}

/* 小程序预览样式 */
.miniprogram-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 40rpx;
  background-color: #fff;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.file-icon-pdf {
  background-color: #ff4d4f;
}

.file-icon-word {
  background-color: #1890ff;
}

.file-icon-excel {
  background-color: #52c41a;
}

.file-icon-default {
  background-color: #8c8c8c;
}

.file-type-text {
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  text-align: center;
}

.file-details {
  flex: 1;
  overflow: hidden;
}

.file-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: auto;
}

.preview-btn {
  flex: 1;
  background-color: var(--nuodun-primary-color, #1890ff);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.preview-btn:active {
  background-color: var(--nuodun-primary-color-dark, #096dd9);
}

.download-btn.secondary {
  flex: 1;
  background-color: transparent;
  color: var(--nuodun-primary-color, #1890ff);
  border: 2rpx solid var(--nuodun-primary-color, #1890ff);
}

.download-btn.secondary:active {
  background-color: rgba(var(--nuodun-primary-color-rgb, 24, 144, 255), 0.1);
}
</style>