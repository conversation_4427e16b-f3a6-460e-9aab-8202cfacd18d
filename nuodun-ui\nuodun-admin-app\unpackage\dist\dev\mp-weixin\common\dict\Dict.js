"use strict";const e=require("../vendor.js"),t=require("./DictMeta.js"),s=require("./Func.js"),r=require("../../api/dicts.js"),i={},a={types:[]};function c(e,t){return t.request(t).then((s=>{const r=t.type;let i=t.responseConverter(s,t);return e.type[r].splice(0,Number.MAX_SAFE_INTEGER,...i),i.forEach((t=>{e.label[r][t.value]=t.label})),i}))}exports.Dict=class{constructor(){this.owner=null,this.label=e.reactive({}),this.type=e.reactive({})}init(e){e instanceof Array&&(e={types:e});const r=s.mergeRecursive(a,e);if(void 0===r.types)throw new Error("need dict types");const i=[];return this._dictMetas=r.types.map((e=>t.DictMeta.parse(e))),this._dictMetas.forEach((e=>{const t=e.type;this.label[t]={},this.type[t]=[],e.lazy||i.push(c(this,e))})),Promise.all(i)}reloadDict(e){const t=this._dictMetas.find((t=>t.type===e));return void 0===t?Promise.reject(`the dict meta of ${e} was not found`):c(this,t)}},exports.useDict=function(...t){const s=e.ref({});return t.forEach((t=>{s.value[t]=[],i[t]?s.value[t]=i[t]:r.getDicts(t).then((e=>{if(200===e.code&&e.data){const r=e.data.map((e=>({label:e.dictLabel,value:e.dictValue,elTagType:e.listClass,elTagClass:e.cssClass})));s.value[t]=r,i[t]=r}})).catch((s=>{e.index.__f__("error","at common/dict/Dict.js:107",`获取字典[${t}]失败`,s)}))})),e.toRefs(s.value)};
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/dict/Dict.js.map
