"use strict";const e=require("../common/vendor.js"),t=require("../common/toast.js"),o=require("../common/common.js"),n=require("../config/environment.js"),r=require("../config/token.js"),s=require("../store/store.js");let a=!1;const i={401:"请先登录",403:"当前操作没有权限",404:"访问资源不存在",default:"系统未知错误，请反馈给管理员"},d=n.environment.aesKey,c=()=>({"APP-Authorization":"Bearer "+r.getToken()}),p=r=>{const{url:p,method:u="GET",data:l={},params:m=null,header:y={},noToken:h=!1,isLoading:f=!1,loadingTitle:g="加载中...",baseUrl:S=n.environment.baseUrl,encrypt:C=n.environment.enableRequestEncrypt}=r,j={...y};h||Object.assign(j,c()),f&&e.index.showLoading(g);let x=p;if(m){x=p+"?";for(const e of Object.keys(m)){const t=m[e],o=encodeURIComponent(e)+"=";if(null!=t)if("object"==typeof t){for(const n of Object.keys(t))if(null!==t[n]&&void 0!==t[n]){const o=encodeURIComponent(e+"["+n+"]")+"=";x+=o+encodeURIComponent(t[n])+"&"}}else x+=o+encodeURIComponent(t)+"&"}x=x.slice(0,-1)}let J=l;if(C&&("POST"===u.toUpperCase()||"PUT"===u.toUpperCase())&&Object.keys(l).length>0){const t=(t=>{if(!t)return null;try{const o="object"==typeof t?JSON.stringify(t):t,n=e.CryptoJS.SHA256(d),r=e.CryptoJS.enc.Hex.parse(n.toString().substring(0,32));return e.CryptoJS.AES.encrypt(o,r,{mode:e.CryptoJS.mode.ECB,padding:e.CryptoJS.pad.Pkcs7}).toString()}catch(o){return e.index.__f__("error","at api/request.js:52","加密失败:",o),null}})(l);t&&(j["X-Encrypted"]="true",J={encryptedData:t},j["Content-Type"]="application/json;charset=UTF-8")}return new Promise(((n,r)=>{e.index.request({method:u.toUpperCase(),url:S+x,data:J,header:j,dataType:"json",success:c=>{if(e.index.stopPullDownRefresh(),f&&e.index.hideLoading(),c.data&&!0===c.data.encrypted&&c.data.data)try{const o=(t=>{if(!t)return null;try{const o=e.CryptoJS.SHA256(d),n=e.CryptoJS.enc.Hex.parse(o.toString().substring(0,32));return e.CryptoJS.AES.decrypt(t,n,{mode:e.CryptoJS.mode.ECB,padding:e.CryptoJS.pad.Pkcs7}).toString(e.CryptoJS.enc.Utf8)}catch(o){return e.index.__f__("error","at api/request.js:83","解密失败:",o),null}})(c.data.data);if(!o)return t.toast.show("数据解密失败"),void r("数据解密失败");try{c.data=JSON.parse(o)}catch(l){return t.toast.show("数据解析失败"),void r("数据解析失败")}}catch(m){return t.toast.show("数据解密失败"),void r("数据解密失败")}const p=c.data.code||200,u=i[p]||c.data.msg||i.default;401!==p||a?500===p||200!==p?(t.toast.show(u),r(c.data)):n(c.data):(a=!0,o.showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then((t=>{t.confirm&&s.store.dispatch("LogOut").then((()=>{e.index.reLaunch({url:"/pages/login/login"})}))})).catch((()=>{a=!1})),r("无效的会话，或者会话已过期，请重新登录。"))},fail:o=>{e.index.stopPullDownRefresh(),f&&e.index.hideLoading();let n=o.errMsg||"网络请求失败";n.includes("timeout")?n="请求超时，请检查网络":n.includes("fail")&&(n="网络连接失败，请检查网络设置"),t.toast.show(n),r(o)}})}))};exports.get=(e,t={},o={})=>p({url:e,method:"GET",params:t,...o}),exports.getAuthHeader=c,exports.post=(e,t={},o={})=>p({url:e,method:"POST",data:t,...o}),exports.request=p;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/request.js.map
