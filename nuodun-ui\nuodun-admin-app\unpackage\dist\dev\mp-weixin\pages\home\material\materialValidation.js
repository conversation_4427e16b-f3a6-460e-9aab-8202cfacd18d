"use strict";require("../../../common/vendor.js"),require("../../../common/toast.js"),require("../../../api/request.js");exports.buildSubmitData=e=>{const t=[];return Object.values(e).forEach((e=>{if(e.isModified){const i={id:e.id,materialOptId:e.materialOptId,materialType:e.materialType,description:e.description,planType:e.planType||"1",fileList:e.fileList||[],textData:e.textDataList||[{key:"",value:""}]};t.push(i)}})),t},exports.validateRequiredMaterials=(e,t,i)=>{const a=e.filter((e=>"Y"===e.isRequired));for(const s of a){const e=t[s.id];if(e&&e.isModified){if("1"===s.dataType){if(!e.fileList||0===e.fileList.length)return i.show(`请上传必填材料：${s.materialType}`),!1}else if("2"===s.dataType){if(!(e.textDataList&&e.textDataList.some((e=>e.key&&e.value))))return i.show(`请填写必填材料：${s.materialType}`),!1}}else if("1"===s.dataType){if(!s.fileList||0===s.fileList.length)return i.show(`请上传必填材料：${s.materialType}`),!1}else if("2"===s.dataType){if(!(s.textDataList&&s.textDataList.some((e=>e.key&&e.value))))return i.show(`请填写必填材料：${s.materialType}`),!1}}return!0};
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/materialValidation.js.map
