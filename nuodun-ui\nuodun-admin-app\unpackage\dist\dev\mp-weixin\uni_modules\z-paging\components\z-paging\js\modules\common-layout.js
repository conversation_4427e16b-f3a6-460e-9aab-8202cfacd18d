"use strict";const e=require("../../../../../../common/vendor.js"),t=require("../z-paging-utils.js"),s={data:()=>({systemInfo:null,cssSafeAreaInsetBottom:-1,isReadyDestroy:!1}),computed:{windowTop(){return this.systemInfo&&this.systemInfo.windowTop||0},safeAreaBottom(){if(!this.systemInfo)return 0;let e=0;return e=Math.max(this.cssSafeAreaInsetBottom,0),e},isOldWebView(){try{const e=t.u.getSystemInfoSync(!0).system.split(" "),s=e[0],o=parseInt(e[1]);if("iOS"===s&&o<=10||"Android"===s&&o<=6)return!0}catch(e){return!1}return!1},zSlots(){return this.$slots}},beforeDestroy(){this.isReadyDestroy=!0},unmounted(){this.isReadyDestroy=!0},methods:{updateFixedLayout(){this.fixed&&this.$nextTick((()=>{this.systemInfo=t.u.getSystemInfoSync()}))},_getNodeClientRect(t,s=!0,o=!1){if(this.isReadyDestroy)return Promise.resolve(!1);let i=s?e.index.createSelectorQuery().in(!0===s?this:s):e.index.createSelectorQuery();return o?i.select(t).scrollOffset():i.select(t).boundingClientRect(),new Promise(((e,t)=>{i.exec((t=>{e(!(!t||""==t||null==t||!t.length)&&t)}))}))},_updateLeftAndRightWidth(e,t){this.$nextTick((()=>{setTimeout((()=>{["left","right"].map((s=>{this._getNodeClientRect(`.${t}-${s}`).then((t=>{this.$set(e,s,t?t[0].width+"px":"0px")}))}))}),0)}))},_getCssSafeAreaInsetBottom(e){this._getNodeClientRect(".zp-safe-area-inset-bottom").then((t=>{this.cssSafeAreaInsetBottom=t?t[0].height:-1,t&&e&&e()}))},_getSystemInfoSync:(e=!1)=>t.u.getSystemInfoSync(e)}};exports.commonLayoutModule=s;
//# sourceMappingURL=../../../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/js/modules/common-layout.js.map
