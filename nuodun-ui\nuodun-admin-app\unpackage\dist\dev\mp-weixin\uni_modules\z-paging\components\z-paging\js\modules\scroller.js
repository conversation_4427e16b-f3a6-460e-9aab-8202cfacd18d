"use strict";const t=require("../../../../../../common/vendor.js"),l=require("../z-paging-utils.js"),o=require("../z-paging-enum.js"),e={props:{usePageScroll:{type:Boolean,default:l.u.gc("usePageScroll",!1)},scrollable:{type:Boolean,default:l.u.gc("scrollable",!0)},showScrollbar:{type:Boolean,default:l.u.gc("showScrollbar",!0)},scrollX:{type:Boolean,default:l.u.gc("scrollX",!1)},scrollToTopBounceEnabled:{type:Boolean,default:l.u.gc("scrollToTopBounceEnabled",!1)},scrollToBottomBounceEnabled:{type:Boolean,default:l.u.gc("scrollToBottomBounceEnabled",!0)},scrollWithAnimation:{type:<PERSON><PERSON><PERSON>,default:l.u.gc("scrollWithAnimation",!1)},scrollIntoView:{type:String,default:l.u.gc("scrollIntoView","")}},data:()=>({scrollTop:0,oldScrollTop:0,scrollLeft:0,oldScrollLeft:0,scrollViewStyle:{},scrollViewContainerStyle:{},scrollViewInStyle:{},pageScrollTop:-1,scrollEnable:!0,privateScrollWithAnimation:-1,cacheScrollNodeHeight:-1,superContentHeight:0,lastScrollHeight:0,lastScrollDirection:"",setContentHeightPending:!1}),watch:{oldScrollTop(t){!this.usePageScroll&&this._scrollTopChange(t,!1)},pageScrollTop(t){this.usePageScroll&&this._scrollTopChange(t,!0)},usePageScroll:{handler(t){this.loaded&&this.autoHeight&&this._setAutoHeight(!t)},immediate:!0},finalScrollTop(t){this.renderPropScrollTop=t<6?0:10}},computed:{finalScrollWithAnimation(){return-1!==this.privateScrollWithAnimation?1===this.privateScrollWithAnimation:this.scrollWithAnimation},finalScrollViewStyle(){return 1!=this.superContentZIndex&&(this.scrollViewStyle["z-index"]=this.superContentZIndex,this.scrollViewStyle.position="relative"),this.scrollViewStyle},finalScrollTop(){return this.usePageScroll?this.pageScrollTop:this.oldScrollTop},finalIsOldWebView(){return this.isOldWebView&&!this.usePageScroll},finalScrollable(){return this.scrollable&&!this.usePageScroll&&this.scrollEnable&&(!!this.refresherCompleteScrollable||this.refresherStatus!==o.Enum.Refresher.Complete)&&(!!this.refresherRefreshingScrollable||this.refresherStatus!==o.Enum.Refresher.Loading)}},methods:{scrollToTop(t,l=!0){this.useChatRecordMode&&l&&!this.isChatRecordModeAndNotInversion?this.scrollToBottom(t,!1):this.$nextTick((()=>{this._scrollToTop(t,!1)}))},scrollToBottom(t,l=!0){this.useChatRecordMode&&l&&!this.isChatRecordModeAndNotInversion?this.scrollToTop(t,!1):this.$nextTick((()=>{this._scrollToBottom(t)}))},scrollIntoViewById(t,l,o){this._scrollIntoView(t,l,o)},scrollIntoViewByNodeTop(t,l,o){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollIntoViewByNodeTop(t,l,o)}))},scrollToY(t,l,o){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollToY(t,l,o)}))},scrollToX(t,l,o){this.scrollLeft=this.oldScrollLeft,this.$nextTick((()=>{this._scrollToX(t,l,o)}))},scrollIntoViewByIndex(t,e,i){t>=this.realTotalData.length?l.u.consoleErr("当前滚动的index超出已渲染列表长度，请先通过refreshToPage加载到对应index页并等待渲染成功后再调用此方法！"):this.$nextTick((()=>{if(this.finalUseVirtualList){const s=this.cellHeightMode===o.Enum.CellHeightMode.Fixed;l.u.delay((()=>{if(this.finalUseVirtualList){const l=s?this.virtualCellHeight*t:this.virtualHeightCacheList[t].lastTotalHeight;this.scrollToY(l,e,i)}}),s?0:100)}}))},scrollIntoViewByView(t,l,o){this._scrollIntoView(t,l,o)},updatePageScrollTop(t){this.pageScrollTop=t},updatePageScrollTopHeight(){this._updatePageScrollTopOrBottomHeight("top")},updatePageScrollBottomHeight(){this._updatePageScrollTopOrBottomHeight("bottom")},updateLeftAndRightWidth(){this.finalIsOldWebView&&this.$nextTick((()=>this._updateLeftAndRightWidth(this.scrollViewContainerStyle,"zp-page")))},updateScrollViewScrollTop(t,l=!0){this._updatePrivateScrollWithAnimation(l),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=t,this.oldScrollTop=this.scrollTop}))},_onScrollToUpper(){this._emitScrollEvent("scrolltoupper"),this.$emit("scrollTopChange",0),this.$nextTick((()=>{this.oldScrollTop=0}))},_onScrollToLower(t){(!t.detail||!t.detail.direction||"bottom"===t.detail.direction)&&this.toBottomLoadingMoreEnabled&&this._onLoadingMore(this.useChatRecordMode?"click":"toBottom")},_scrollToTop(l=!0,o=!0){this.usePageScroll?this.$nextTick((()=>{t.index.pageScrollTo({scrollTop:0,duration:l?100:0})})):(this._updatePrivateScrollWithAnimation(l),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=0,this.oldScrollTop=this.scrollTop})))},async _scrollToBottom(l=!0){if(this.usePageScroll)this.$nextTick((()=>{t.index.pageScrollTo({scrollTop:Number.MAX_VALUE,duration:l?100:0})}));else try{this._updatePrivateScrollWithAnimation(l);const t=await this._getNodeClientRect(".zp-paging-container"),o=await this._getNodeClientRect(".zp-scroll-view"),e=t?t[0].height:0,i=o?o[0].height:0;e>i&&(this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=e-i+this.virtualPlaceholderTopHeight,this.oldScrollTop=this.scrollTop})))}catch(o){}},_scrollIntoView(t,o=0,e=!1,i){try{this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._getNodeClientRect("#"+t.replace("#",""),!1).then((s=>{s?this._getNodeClientRect(".zp-scroll-view-container").then((t=>{t&&(this._scrollIntoViewByNodeTop(s[0].top-t[0].top,o,e),i&&i())})):l.u.consoleErr(`无法获取${t}的节点信息，请检查！`)}))}))}catch(s){}},_scrollIntoViewByNodeTop(t,l=0,o=!1){this.isChatRecordModeAndInversion?this._getNodeClientRect(".zp-scroll-view").then((e=>{e&&this._scrollToY(e[0].height-t,l,o,!0)})):this._scrollToY(t,l,o,!0)},_scrollToY(o,e=0,i=!1,s=!1){this._updatePrivateScrollWithAnimation(i),l.u.delay((()=>{if(this.usePageScroll){s&&-1!==this.pageScrollTop&&(o+=this.pageScrollTop);const l=o-e;t.index.pageScrollTo({scrollTop:l,duration:i?100:0})}else s&&(o+=this.oldScrollTop),this.scrollTop=o-e}),10)},_scrollToX(t,o=0,e=!1){this._updatePrivateScrollWithAnimation(e),l.u.delay((()=>{this.usePageScroll?l.u.consoleErr("使用页面滚动时不支持scrollToX"):this.scrollLeft=t-o}),10)},_scroll(t){this.$emit("scroll",t);const{scrollTop:o,scrollLeft:e,scrollHeight:i}=t.detail;if(this.watchScrollDirectionChange){let t=this.oldScrollTop>o?"top":"bottom";(o<=0||!this.scrollEnable)&&(t="top"),o>this.lastScrollHeight-this.scrollViewHeight-1&&this.scrollEnable&&(t="bottom"),t!==this.lastScrollDirection&&(this.$emit("scrollDirectionChange",t),this.lastScrollDirection=t),this.lastScrollHeight===i||this.setContentHeightPending||(this.setContentHeightPending=!0,l.u.delay((()=>{this.lastScrollHeight=i,this.setContentHeightPending=!1})))}this.finalUseVirtualList&&this._updateVirtualScroll(o,this.oldScrollTop-o),this.oldScrollTop=o,this.oldScrollLeft=e;const s=t.detail.scrollHeight-this.oldScrollTop;!this.isIos&&this._checkScrolledToBottom(s)},_emitScrollEvent(t){const l="scrolltolower"===t?"scrolltoupper":"scrolltolower",o=this.useChatRecordMode&&!this.isChatRecordModeAndNotInversion?l:t;this.$emit(o)},_updatePrivateScrollWithAnimation(t){this.privateScrollWithAnimation=t?1:0,l.u.delay((()=>this.$nextTick((()=>{this.privateScrollWithAnimation=-1}))),100,"updateScrollWithAnimationDelay")},_doCheckScrollViewShouldFullHeight(t){this.autoFullHeight&&this.usePageScroll&&this.isTotalChangeFromAddData?this.$nextTick((()=>{this._checkScrollViewShouldFullHeight(((l,o)=>{this._preCheckShowNoMoreInside(t,l,o)}))})):this._preCheckShowNoMoreInside(t)},async _checkScrollViewShouldFullHeight(t){try{const l=await this._getNodeClientRect(".zp-scroll-view"),o=await this._getNodeClientRect(".zp-paging-container-content");if(!l||!o)return;const e=o[0].height,i=l[0].top;this.isAddedData&&e+i<=this.windowHeight?(this._setAutoHeight(!0,l),t(l,o)):(this._setAutoHeight(!1),t(null,null))}catch(l){t(null,null)}},async _updateCachedSuperContentHeight(){const t=await this._getNodeClientRect(".z-paging-content");t&&(this.superContentHeight=t[0].height)},_scrollTopChange(t,l){this.$emit("scrollTopChange",t),this.$emit("update:scrollTop",t),this._checkShouldShowBackToTop(t);const o=t>5?6:0;l&&this.wxsPageScrollTop!==o?this.wxsPageScrollTop=o:l||this.wxsScrollTop===o||(this.wxsScrollTop=o,o>6&&(this.scrollEnable=!0))},_updatePageScrollTopOrBottomHeight(t){if(!this.usePageScroll)return;this._doCheckScrollViewShouldFullHeight(this.realTotalData);const o=`.zp-page-${t}`,e=`margin${t.slice(0,1).toUpperCase()+t.slice(1)}`,i=this.safeAreaInsetBottom&&!this.zSlots.bottom&&!this.useSafeAreaPlaceholder;this.$nextTick((()=>{l.u.delay((()=>{this._getNodeClientRect(o).then((l=>{if(l){let o=l[0].height;"bottom"===t?i&&(o+=this.safeAreaBottom):this.cacheTopHeight=o,this.$set(this.scrollViewStyle,e,`${o}px`)}else i&&this.$set(this.scrollViewStyle,e,`${this.safeAreaBottom}px`)}))}),0)}))}}};exports.scrollerModule=e;
//# sourceMappingURL=../../../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/js/modules/scroller.js.map
