"use strict";const t=require("../../../common/vendor.js"),e={name:"popup-layer",props:{direction:{type:String,default:"top"},autoClose:{type:Boolean,default:!0},isTransNav:{type:<PERSON>olean,default:!1},navHeight:{type:Number,default:0}},data:()=>({ifshow:!1,translateValue:-100,timer:null,iftoggle:!1}),computed:{_translate(){if(this.isTransNav){return{top:`transform:translateY(${-this.translateValue}%)`,bottom:`transform:translateY(calc(${this.translateValue}% + ${this.navHeight}px))`,left:`transform:translateX(${-this.translateValue}%)`,right:`transform:translateX(${this.translateValue}%)`}[this.direction]}return{top:`transform:translateY(${-this.translateValue}%)`,bottom:`transform:translateY(${this.translateValue}%)`,left:`transform:translateX(${-this.translateValue}%)`,right:`transform:translateX(${this.translateValue}%)`}[this.direction]},_location(){return{top:"bottom:0px;width:100%;",bottom:"top:0px;width:100%;",left:"right:0px;height:100%;",right:"left:0px;height:100%;"}[this.direction]+this._translate}},methods:{show(){this.ifshow=!0,setTimeout((()=>{this.translateValue=0}),100),setTimeout((()=>{this.iftoggle=!0}),300)},close(){null===this.timer&&this.iftoggle&&(this.translateValue=-100-this.navHeight,this.timer=setTimeout((()=>{this.ifshow=!1,this.timer=null,this.iftoggle=!1}),300),this.$emit("close"))},ableClose(){this.autoClose&&this.close()},stopEvent(t){}}};const s=t._export_sfc(e,[["render",function(e,s,a,o,i,r){return{a:t.o(((...t)=>r.stopEvent&&r.stopEvent(...t))),b:t.s(r._location),c:i.ifshow,d:t.o(((...t)=>r.ableClose&&r.ableClose(...t))),e:t.o((()=>{})),f:t.gei(e,"")}}]]);wx.createComponent(s);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/songlazy-sl-filter/sl-filter/popup-layer.js.map
