"use strict";const t=require("../../../common/vendor.js"),e=require("../../../common/assets.js"),o=require("../../../api/home/<USER>");if(!Array){t.resolveComponent("dict-tag")()}const s={__name:"chan",setup(s,{expose:a}){const{proxy:l}=t.getCurrentInstance(),{st_follow_status:r,t_st_follow_type:n}=l.useDict("st_follow_status","t_st_follow_type"),i=t.ref(64),c=t.ref([]),_=(e,o={})=>{let s=e;if(Object.keys(o).length>0){s+=`?${Object.entries(o).map((([t,e])=>`${t}=${e}`)).join("&")}`}t.index.navigateTo({url:s})},p=async()=>{try{const t=await o.getFollowList({pageNum:1,pageSize:5});c.value=t.rows}catch(e){t.index.__f__("error","at pages/tabbar/home/<USER>","获取最近跟进列表失败",e)}};return t.onMounted((()=>{l&&l.$dict&&"function"==typeof l.$dict.loadDict&&l.$dict.loadDict(["st_follow_status","st_follow_type"]);const e=t.index.getSystemInfoSync();i.value=e.statusBarHeight+44,p()})),a({refresh:async()=>{try{return await Promise.all([p()]),!0}catch(e){return t.index.__f__("error","at pages/tabbar/home/<USER>","刷新渠道首页数据失败",e),!1}}}),(o,s)=>({a:e._imports_0$3,b:t.o((t=>_("/pages/home/<USER>/list"))),c:e._imports_1$1,d:t.o((t=>_("/pages/home/<USER>/list"))),e:t.o((t=>_("/pages/home/<USER>/list"))),f:t.f(c.value,((e,o,s)=>t.e$1({a:t.t(e.stName),b:t.t(e.createTime),c:e.stFollowType},e.stFollowType?{d:"362f4ec7-0-"+s,e:t.p({options:t.unref(n),value:e.stFollowType})}:{},{f:e.stFollowStatus},e.stFollowStatus?{g:"362f4ec7-1-"+s,h:t.p({options:t.unref(r),value:e.stFollowStatus,showTag:!0,maxLength:8})}:{},{i:o,j:t.o((t=>_("/pages/home/<USER>/detail",{id:e.id})),o)}))),g:t.gei(o,"")})}},a=t._export_sfc(s,[["__scopeId","data-v-362f4ec7"]]);wx.createComponent(a);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/tabbar/home/<USER>
