"use strict";const e=require("../../common/vendor.js"),a=require("../../config/environment.js");require("../../api/request.js");const t=require("../../common/auth.js"),n=require("../../common/common.js"),r={__name:"mine",setup(r,{expose:o}){const{proxy:s}=e.getCurrentInstance(),u=s.toast,i=e.useStore(),c=e.ref("");a.environment.fileUrl;const l=a.environment.fileUrl,m=e.ref(""),v=e.ref(0),d=e.computed((()=>!!i.state.token)),p=e.computed((()=>t.hasRoleType(t.ROLE_TYPES.CHAN))),f=()=>{if(d.value){const e=i.state.userInfo;c.value=e.avatar,v.value=e.sex,m.value=e.nickName}},g=()=>{d.value?e.index.navigateTo({url:"/pages/mine/resetPwd/resetPwdByCode"}):w()},h=()=>{d.value?I():w()},x=()=>{d.value?e.index.navigateTo({url:"/pages/mine/agreements/chan/agreementList"}):w()},_=()=>{d.value?b():w()},w=()=>{e.index.navigateTo({url:"/pages/login/login"})},I=()=>{e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success(e){y(e.tempFilePaths)}})},y=t=>{e.index.showLoading("上传中..."),e.index.uploadFile({url:a.environment.baseUrl+"/system/user/profile/avatar",filePath:t[0],name:"avatarfile",header:{"APP-Authorization":"Bearer "+i.state.token},success:e=>{const a=JSON.parse(e.data);200===a.code?i.dispatch("GetInfo",{isJump:!1}).then((()=>{c.value=i.state.userInfo.avatar,u.show("头像更新成功")})):u.show(a.msg||"上传失败")},fail:e=>{u.show("上传失败，请重试")},complete:()=>{e.index.hideLoading()}})},b=()=>{n.showConfirm("确认退出登录吗?").then((e=>{e.confirm&&i.dispatch("LogOut")}))};return e.onMounted((()=>{f()})),o({refresh:async()=>{try{return d.value&&(await i.dispatch("GetInfo",{isJump:!1}),f()),!0}catch(a){return e.index.__f__("error","at pages/tabbar/mine.vue:105","刷新个人信息失败",a),!1}}}),(a,t)=>e.e$1({a:e.s(d.value?"background: url("+e.unref(l)+c.value+") no-repeat center/cover #eeeeee;":"background: #eeeeee;"),b:e.t(d.value?m.value||"未设置昵称":"未登录"),c:e.t(d.value?"ID: "+e.unref(i).state.userInfo.userName:"请先登录"),d:e.t(d.value?"去修改":"请先登录"),e:e.o(g),f:e.t(d.value?"去上传":"请先登录"),g:e.o(h),h:p.value},p.value?{i:e.t(d.value?"查看":"请先登录"),j:e.o(x)}:{},{k:e.t(d.value?"退出登录":"去登录"),l:e.o(_),m:e.gei(a,"")})}},o=e._export_sfc(r,[["__scopeId","data-v-3a6ac8e1"]]);wx.createComponent(o);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tabbar/mine.js.map
