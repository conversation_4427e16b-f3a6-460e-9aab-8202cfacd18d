"use strict";const t=require("./Dict.js"),i=require("./DictOptions.js");exports.DataDict=function(s,n){i.mergeOptions(n),s.mixin({data(){if(void 0===this.$options.dicts||null===this.$options.dicts)return{};const i=new t.Dict;return i.owner=this,{dict:i}},created(){this.dict instanceof t.Dict&&(n.onCreated&&n.onCreated(this.dict),this.dict.init(this.$options.dicts).then((()=>{n.onReady&&n.onReady(this.dict),this.$nextTick((()=>{this.$emit("dictReady",this.dict),this.$options.methods&&this.$options.methods.onDictReady instanceof Function&&this.$options.methods.onDictReady.call(this,this.dict)}))})))}})};
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/dict/index.js.map
