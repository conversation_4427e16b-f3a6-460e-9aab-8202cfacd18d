"use strict";const e=require("./Func.js"),t=require("./DictOptions.js");class s{constructor(e){this.type=e.type,this.request=e.request,this.responseConverter=e.responseConverter,this.labelField=e.labelField,this.valueField=e.valueField,this.lazy=!0===e.lazy}}s.parse=function(r){let i=null;return"string"==typeof r?(i=t.options.metas[r]||{},i.type=r):"object"==typeof r&&(i=r),i=e.mergeRecursive(t.options.metas["*"],i),new s(i)},exports.DictMeta=s;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/dict/DictMeta.js.map
