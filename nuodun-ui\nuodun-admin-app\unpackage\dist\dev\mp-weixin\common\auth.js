"use strict";const t=require("./vendor.js"),e=require("../store/store.js"),r=require("../config/environment.js"),n={NORM:"NORM",CHAN:"CHAN",STU:"STU",FAM:"FAM"};function o(){var t;return(null==(t=e.store.state.userInfo)?void 0:t.roleType)||n.NORM}exports.ROLE_TYPES=n,exports.encryptAndStore=function(e,n){const o=function(e,r){const n=t.CryptoJS.enc.Utf8.parse(r);return t.CryptoJS.AES.encrypt(e,n,{mode:t.CryptoJS.mode.ECB,padding:t.CryptoJS.pad.Pkcs7}).toString()}(JSON.stringify(n),r.environment.aesKey);t.index.setStorageSync(e,o)},exports.getAndDecrypt=function(e){const n=t.index.getStorageSync(e);if(!n)return null;try{const e=function(e,r){const n=t.CryptoJS.enc.Utf8.parse(r);return t.CryptoJS.AES.decrypt(e,n,{mode:t.CryptoJS.mode.ECB,padding:t.CryptoJS.pad.Pkcs7}).toString(t.CryptoJS.enc.Utf8)}(n,r.environment.aesKey);return JSON.parse(e)}catch(o){return t.index.__f__("error","at common/auth.js:119","解密数据格式错误",o),null}},exports.getUserRoleType=o,exports.hasPerimission=function(t){const r=e.store.state&&e.store.state.permissions;if(!(t&&t instanceof Array&&t.length>0))throw new Error("请设置操作权限标签值");{const e=t;if(r.some((t=>"*:*:*"===t||e.includes(t))))return!0}return!1},exports.hasRole=function(t){const r=e.store.state&&e.store.state.roles;if(!(t&&t instanceof Array&&t.length>0))throw new Error('请设置角色权限标签值"');{const e=t;if(r.some((t=>"admin"===t||e.includes(t))))return!0}return!1},exports.hasRoleType=function(t){const e=o();return Array.isArray(t)?t.includes(e):e===t};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/auth.js.map
