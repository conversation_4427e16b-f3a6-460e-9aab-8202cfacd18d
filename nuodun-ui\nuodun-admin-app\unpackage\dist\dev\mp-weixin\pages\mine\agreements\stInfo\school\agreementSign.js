"use strict";const e=require("../../../../../common/vendor.js"),a=require("../../../../../api/home/<USER>"),t=require("../../../../../common/request.js"),n=require("../../../../../common/modal.js"),o=require("../../../../../config/environment.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../../../../components/custom-nav/custom-nav.js")+(()=>"../../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+l+u)();const u=()=>"../../../../../components/htz-signature/htz-signature.js",l=()=>"../../../../../components/document-preview/index.js",i={__name:"agreementSign",setup(u){const{proxy:l}=e.getCurrentInstance(),i=l.toast,r=o.environment.fileUrl||"",v=e.ref(null),s=e.ref(""),m=e.ref(!1),c=e.ref(!1),d={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},g=e.ref(""),f=e.ref(""),h=e.ref(""),p=e.ref(""),x=e.ref({trustee:!1,guardian:!1}),_=e.ref(!1),w=e.ref(!1),P=e.ref(!1),C=e.ref({confirmationFilePath:"",confirmationJson:""}),F=e.ref(""),I=e.ref(null),T=e.computed((()=>h.value&&p.value)),j=e.computed((()=>x.value.trustee||x.value.guardian)),S=e.computed((()=>C.value.confirmationFilePath)),q=a=>{F.value=a,c.value=!0,e.nextTick$1((()=>{I.value}))},L=()=>{I.value&&I.value.cleanup&&I.value.cleanup(),c.value=!1,setTimeout((()=>{e.index.pageScrollTo({scrollTop:0,duration:0})}),300)},y=a=>{var n,o;a&&a.tempFilePath&&("trustee"===F.value?(g.value=a.tempFilePath,x.value.trustee=!0):"guardian"===F.value&&(f.value=a.tempFilePath,x.value.guardian=!0),n=a.tempFilePath,o=F.value,t.upload({filePath:n,name:"file",formData:{fileBizType:"contract_confirmation_image"},success:e=>{e&&200===e.code&&e.data?(P.value=!0,"trustee"===o?h.value=e.data.filePath:"guardian"===o&&(p.value=e.data.filePath),C.value.confirmationFilePath="",C.value.confirmationJson="",i.show("上传成功")):(P.value=!1,"trustee"===o?g.value="":"guardian"===o&&(f.value=""),i.show(e&&e.msg?e.msg:"上传失败"))},fail:a=>{P.value=!1,e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementSign.vue:233","上传请求失败:",a),"trustee"===o?g.value="":"guardian"===o&&(f.value=""),i.show("上传失败")},complete:()=>{"trustee"===o?x.value.trustee=!1:"guardian"===o&&(x.value.guardian=!1),e.index.hideLoading()}})),L()},k=a=>{e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementSign.vue:276","签名失败:",a),i.show("签名失败"),L()},z=()=>{P.value?n.createModal({title:"重新生成",content:"确定要重新生成协议文件吗？",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&(C.value={confirmationFilePath:"",confirmationJson:""},A())})):i.show("请重新上传签名")},A=()=>{j.value?i.show("请等待图片上传完成"):T.value?(_.value=!0,e.index.showLoading("生成协议中..."),a.generateSignedConfirmation(v.value,{trusteeNameImg:h.value,guardianNameImg:p.value}).then((e=>{200===e.code&&e.data&&(P.value=!1,C.value={...C.value,...e.data},i.show("协议生成成功"))})).catch((a=>{P.value=!0,e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementSign.vue:341","生成协议失败:",a)})).finally((()=>{_.value=!1,e.index.hideLoading()}))):i.show("请先上传委托人和监护人签名")},B=()=>{n.createModal({title:"确认提交",content:"确定要提交该协议吗？提交后将不可修改",confirmText:"确定",cancelText:"取消"}).then((e=>{e.confirm&&N()}))},N=()=>{S.value?(w.value=!0,e.index.showLoading("提交中..."),a.confirmSignConfirmation({id:v.value,trusteeNameImg:h.value,guardianNameImg:p.value,confirmationConfirmStatus:"2",...C.value}).then((a=>{200===a.code&&(i.show("协议提交成功"),setTimeout((()=>{e.index.navigateBack()}),500))})).catch((a=>{e.index.__f__("error","at pages/mine/agreements/stInfo/school/agreementSign.vue:405","提交协议失败:",a)})).finally((()=>{w.value=!1,e.index.hideLoading()}))):i.show("请先生成协议")};return e.onLoad((a=>{a.id?(v.value=a.id,s.value=decodeURIComponent(a.agreementCode||"")):(i.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1e3))})),e.onUnload((()=>{})),(a,t)=>e.e$1({a:e.p({title:"定校协议签署",showLeft:!0,path:"/pages/mine/agreements/stInfo/school/agreementDetail?id="+v.value}),b:m.value},m.value?{c:e.p({status:"loading","content-text":d})}:e.e$1({d:e.t(s.value),e:g.value},g.value?{f:g.value}:{},{g:e.o((e=>q("trustee"))),h:f.value},f.value?{i:f.value}:{},{j:e.o((e=>q("guardian"))),k:C.value.confirmationFilePath},C.value.confirmationFilePath?{l:e.o(z),m:e.p({"file-path":e.unref(r)+C.value.confirmationFilePath,"file-name":"定校协议","allow-download":!0})}:{},{n:!S.value},S.value?{}:{o:e.t(j.value?"图片上传中...":_.value?"生成中...":T.value?"生成协议":"请上传委托人和监护人签名"),p:e.o(A),q:_.value||!T.value||j.value},{r:S.value},S.value?{s:e.t(w.value?"提交中...":"确认提交"),t:e.o(B),v:w.value}:{}),{w:c.value},c.value?{x:e.sr(I,"5b84d53e-3",{k:"htzsignature"}),y:e.o(y),z:e.o(k),A:e.p({cid:"handWriteCanvas",autoRotate:!0,rotateAngle:270}),B:e.o(L)}:{},{C:e.gei(a,"")})}};wx.createPage(i);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/mine/agreements/stInfo/school/agreementSign.js.map
