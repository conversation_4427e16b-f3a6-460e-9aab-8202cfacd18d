"use strict";const e=require("../../../../common/vendor.js"),t=require("../../../../api/mine/userCommissionAgreement.js"),a=require("../../../../config/environment.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("uni-load-more")+e.resolveComponent("uni-popup"))()}Math||((()=>"../../../../components/custom-nav/custom-nav.js")+(()=>"../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+n+(()=>"../../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js"))();const n=()=>"../../../../components/document-preview/index.js",u={__name:"agreementDetail",setup(n){const{proxy:u}=e.getCurrentInstance(),r=u.toast,l=e.ref(null),o=e.ref({}),m=e.ref(!0);a.environment.fileUrl;const s=e.ref(null),v=e.ref(""),c=e.ref(""),i=e.ref(""),d=e.ref(""),g={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},p={S:"中介类",T:"托管类",K:"课程类",Y:"研学类",X:"实习类",U:"科研类"},h=e.computed((()=>"1"===o.value.comStatus||"4"===o.value.comStatus)),f=e.computed((()=>{if(!o.value||!o.value.commissionScaleJson)return[];try{return JSON.parse(o.value.commissionScaleJson).map((e=>{if(!e)return{typeName:"未知",scale:0};const t=e.type||e.key||"";return{typeName:p[t]||e.typeName||t||"未知",scale:e.scale||0}}))}catch(t){return e.index.__f__("error","at pages/mine/agreements/chan/agreementDetail.vue:180","解析佣金比例JSON失败",t),[]}})),S=()=>{m.value=!0,t.getAgreement(l.value).then((e=>{200===e.code?o.value=e.data||{}:r.show("获取合同详情失败")})).catch((t=>{r.show("获取合同详情失败"),e.index.__f__("error","at pages/mine/agreements/chan/agreementDetail.vue:198","获取协议详情失败:",t)})).finally((()=>{m.value=!1}))},w=e=>{switch(e){case"0":return"协议待生成";case"1":return"待签名确认";case"2":return"已签名确认";case"3":return"自主退回";case"4":return"平台驳回";default:return"未知状态"}},A=e=>{switch(e){case"0":case"1":return"status-pending";case"2":return"status-completed";case"3":return"status-returned";case"4":return"status-rejected";default:return""}},_=e=>{if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},j=()=>{v.value="退回确认",c.value="确定要退回该协议吗？",i.value="",d.value="reject",s.value.open()},y=()=>{o.value&&o.value.id?e.index.navigateTo({url:`/pages/mine/agreements/chan/agreementSign?id=${o.value.id}`}):r.show("协议信息不完整")},C=()=>{s.value.close()},P=()=>{if("reject"===d.value){if(!i.value.trim())return void r.show("请输入退回原因");D("3",i.value)}s.value.close()},D=async(a,n)=>{if(o.value&&o.value.id){e.index.showLoading("处理中...");try{await t.updateComStatus({id:o.value.id,comStatus:a,comRejectRemark:n});r.show("退回成功"),setTimeout((()=>{S()}),500)}catch(u){e.index.__f__("error","at pages/mine/agreements/chan/agreementDetail.vue:302","更新协议状态失败:",u)}finally{e.index.hideLoading(),m.value=!1}}else r.show("协议信息不完整")};return e.onLoad((t=>{t.id?(l.value=t.id,S()):(r.show("参数错误"),setTimeout((()=>{e.index.navigateBack()}),1500))})),e.onShow((()=>{l.value&&S()})),(t,a)=>e.e$1({a:e.p({title:"合同信息",showLeft:!0,path:"/pages/mine/agreements/chan/agreementList"}),b:m.value},m.value?{c:e.p({status:"loading","content-text":g})}:e.e$1({d:e.t(o.value.perAgreementCode||o.value.id||"--"),e:e.t(_(o.value.agreementStartDate)||_(o.value.createTime)||"--"),f:e.t(_(o.value.agreementEndDate)||_(o.value.validTime)||"长期有效"),g:e.t(_(o.value.renewalDate)||"--"),h:e.t(w(o.value.comStatus)),i:e.n(A(o.value.comStatus)),j:"3"===o.value.comStatus&&o.value.comRejectRemark},"3"===o.value.comStatus&&o.value.comRejectRemark?{k:e.t(o.value.comRejectRemark)}:{},{l:"4"===o.value.comStatus&&o.value.rebutRemark},"4"===o.value.comStatus&&o.value.rebutRemark?{m:e.t(o.value.rebutRemark)}:{},{n:f.value.length>0},f.value.length>0?{o:e.f(f.value,((t,a,n)=>({a:e.t(t.typeName),b:e.t(t.scale),c:a})))}:{},{p:e.t(o.value.perAgreementCode||"--"),q:o.value.perAgreementPath||o.value.fileUrl},o.value.perAgreementPath||o.value.fileUrl?{r:e.p({"file-path":o.value.perAgreementPath||o.value.fileUrl,"file-name":"渠道合作协议",allowDownload:!0})}:{},{s:o.value.inteAgreementCode||o.value.inteAgreementPath},o.value.inteAgreementCode||o.value.inteAgreementPath?{t:e.t(o.value.inteAgreementCode||"--")}:{},{v:o.value.inteAgreementPath},o.value.inteAgreementPath?{w:e.p({"file-path":o.value.inteAgreementPath,"file-name":"渠道中介协议",allowDownload:!0})}:{},{x:o.value.corAgreementCode||o.value.corAgreementPath},o.value.corAgreementCode||o.value.corAgreementPath?{y:e.t(o.value.corAgreementCode||"--")}:{},{z:o.value.corAgreementPath},o.value.corAgreementPath?{A:e.p({"file-path":o.value.corAgreementPath,"file-name":"渠道课程协议",allowDownload:!0})}:{},{B:h.value},h.value?e.e$1({C:"1"===o.value.comStatus||"4"===o.value.comStatus},"1"===o.value.comStatus||"4"===o.value.comStatus?{D:e.o(j)}:{},{E:"1"===o.value.comStatus||"4"===o.value.comStatus},"1"===o.value.comStatus||"4"===o.value.comStatus?{F:e.o(y)}:{}):{}),{G:e.t(v.value),H:e.t(c.value),I:i.value,J:e.o((e=>i.value=e.detail.value)),K:e.t(i.value.length),L:e.o(C),M:e.o(P),N:e.sr(s,"4d6630b4-5",{k:"customDialog"}),O:e.p({type:"center"}),P:e.gei(t,"")})}},r=e._export_sfc(u,[["__scopeId","data-v-4d6630b4"]]);wx.createPage(r);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/mine/agreements/chan/agreementDetail.js.map
