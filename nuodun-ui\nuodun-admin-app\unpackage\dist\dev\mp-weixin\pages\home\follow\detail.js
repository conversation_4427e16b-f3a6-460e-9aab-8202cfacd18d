"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/home/<USER>");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("dict-tag"))()}Math;const o={__name:"detail",setup(o){const{proxy:a}=e.getCurrentInstance(),l=a.useDict("st_follow_status","st_follow_type","st_intention_type"),s=e.ref(null),n=e.ref(null);e.onLoad((e=>{e.id&&(n.value=e.id,u())}));const u=async()=>{try{const e=await t.getAppFollowDetail(n.value);200===e.code&&(s.value=e.data)}catch(e){a.toast.show("获取详情失败")}};return(t,o)=>e.e$1({a:e.p({title:"跟进详情",showLeft:!0,path:"/pages/home/<USER>/index"}),b:s.value},s.value?e.e$1({c:e.t(s.value.stName),d:e.p({options:e.unref(l).type.st_follow_type,value:s.value.stFollowType}),e:e.p({options:e.unref(l).type.st_follow_status,value:s.value.stFollowStatus}),f:e.p({options:e.unref(l).type.st_intention_type,value:s.value.stIntention}),g:s.value.editText},s.value.editText?{h:s.value.editText}:{},{i:e.t(s.value.createTime)}):{},{j:e.gei(t,"")})}},a=e._export_sfc(o,[["__scopeId","data-v-1d79b74e"]]);wx.createPage(a);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/detail.js.map
