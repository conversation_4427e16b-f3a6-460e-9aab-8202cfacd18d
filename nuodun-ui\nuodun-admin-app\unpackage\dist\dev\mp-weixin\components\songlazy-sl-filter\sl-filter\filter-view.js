"use strict";const e=require("../../../common/vendor.js"),t={data:()=>({selectArr:[],result:{},menuIndex:0,index:0,selectDetailList:[],independenceObj:{},selectedKey:"",cacheSelectedObj:{},defaultSelectedTitleObj:{}}),props:{themeColor:{type:String,default:()=>"#D1372C"},menuList:{type:Array,default:()=>[]},independence:{type:Boolean,default:!1}},computed:{selectedTitleObj(){let e={};for(let t=0;t<this.menuList.length;t++){let i=this.menuList[t];e[i.key]=i.title}return e},defaultSelectedObj(){return this.getSelectedObj()},selectedObj:{get(){return this.getSelectedObj()},set:e=>e}},methods:{getSelectedObj(){let e={};for(let t=0;t<this.menuList.length;t++){let i=this.menuList[t];if(!this.independence&&null!=i.defaultSelectedIndex&&i.defaultSelectedIndex.toString().length>0)if(i.isMutiple){e[i.key]=[],i.detailList[0].isSelected=!1,Array.isArray(i.defaultSelectedIndex)||(i.defaultSelectedIndex=[i.defaultSelectedIndex]);for(let t=0;t<i.defaultSelectedIndex.length;t++)i.detailList[i.defaultSelectedIndex[t]].isSelected=!0,e[i.key].push(i.detailList[i.defaultSelectedIndex[t]].value)}else e[i.key]=i.detailList[i.defaultSelectedIndex].value,this.selectedTitleObj[i.key]=i.detailList[i.defaultSelectedIndex].title,this.defaultSelectedTitleObj[i.key]=i.detailList[i.defaultSelectedIndex].title,i.detailList[0].isSelected=!1,i.detailList[i.defaultSelectedIndex].isSelected=!0;else i.isMutiple?e[i.key]=[]:e[i.key]=""}return this.result=e,e},resetAllSelect(e){let t=[];for(let s=0;s<this.menuList.length;s++)this.resetSelected(this.menuList[s].detailList,this.menuList[s].key),t[this.menuList[s].key]=this.menuList[s].title;let i={result:this.result,titles:t,isReset:!0};this.$emit("confirm",i),e(this.result)},resetSelectToDefault(e){for(let i=0;i<this.menuList.length;i++)if(this.selectDetailList=this.menuList[i].detailList,this.menuList[i].defaultSelectedIndex){if(Array.isArray(this.menuList[i].defaultSelectedIndex))for(let t=0;t<this.menuList[i].defaultSelectedIndex.length;t++)0==this.selectDetailList[this.menuList[i].defaultSelectedIndex[t]].isSelected&&this.itemTap(this.menuList[i].defaultSelectedIndex[t],this.selectDetailList,this.menuList[i].isMutiple,this.menuList[i].key);else this.itemTap(this.menuList[i].defaultSelectedIndex,this.selectDetailList,this.menuList[i].isMutiple,this.menuList[i].key);let e=this.getUnDefaultSelectedIndex(this.menuList[i]);for(let t=0;t<e.length;t++)1==this.selectDetailList[e[t]].isSelected&&this.itemTap(e[t],this.selectDetailList,this.menuList[i].isMutiple,this.menuList[i].key)}this.selectedObj=this.defaultSelectedObj,this.result=this.defaultSelectedObj;let t={result:this.result,titles:this.defaultSelectedTitleObj,isReset:!0};this.$emit("confirm",t),e(this.result)},getUnDefaultSelectedIndex(e){let t=e.defaultSelectedIndex;Array.isArray(t)||(t=[t]);let i=[];for(let s=0;s<e.detailList.length;s++)i.push(s);return t.filter((function(e){return!(i.indexOf(e)>-1)})).concat(i.filter((function(e){return!(t.indexOf(e)>-1)})))},resetMenuList(e){this.menuList=e,this.$emit("update:menuList",e)},menuTabClick(e){if(this.menuIndex=e,this.selectDetailList=this.menuList[e].detailList,this.selectedKey=this.menuList[e].key,this.independence&&!this.menuList[e].isSort)if("{}"==JSON.stringify(this.independenceObj))this.initIndependenceObj(e);else for(let t in this.independenceObj)t!=this.selectedKey&&(this.initIndependenceObj(e),this.resetSelected(this.menuList[e].detailList,this.selectedKey));if(this.independence&&this.menuList[e].isSort&&(this.independenceObj={}),this.independence){let t=this.menuList[e].defaultSelectedIndex;if(null!=t&&t.toString().length>0)if(this.menuList[e].isMutiple)for(let i=0;i<t.length;i++)0==this.menuList[e].detailList[t[i]].isSelected&&this.itemTap(t[i],this.menuList[e].detailList,!0,this.selectedKey);else 0==this.menuList[e].detailList[t].isSelected&&this.itemTap(t,this.menuList[e].detailList,!1,this.selectedKey)}},initIndependenceObj(e){this.independenceObj={},this.menuList[e].isMutiple?this.independenceObj[this.selectedKey]=[]:this.independenceObj[this.selectedKey]=""},itemTap(e,t,i,s){if(1==i)if(t[e].isSelected=!t[e].isSelected,0==e)this.resetSelected(t,s),this.independence||(this.selectedTitleObj[s]=t[e].title);else{if(t[0].isSelected=!1,t[e].isSelected)this.independence?this.independenceObj[this.selectedKey].push(t[e].value):this.selectedObj[s].push(t[e].value);else if(t[e].isSelected=!1,this.independence){var l=this.independenceObj[this.selectedKey].indexOf(t[e].value);this.independenceObj[this.selectedKey].splice(l,1)}else{l=this.selectedObj[s].indexOf(t[e].value);this.selectedObj[s].splice(l,1)}this.independence?this.result=this.independenceObj:this.result=this.selectedObj}else if(0==e)this.resetSelected(t,s),this.independence||(this.selectedTitleObj[s]=t[e].title);else{t[0].isSelected=!1,this.independence?(this.independenceObj[this.selectedKey]=t[e].value,this.result=this.independenceObj):(this.selectedObj[s]=t[e].value,this.result=this.selectedObj,this.menuList.find((e=>e.key===s&&e.reflexTitle))&&(this.selectedTitleObj[s]=t[e].title));for(let i=0;i<t.length;i++)t[i].isSelected=e==i}},resetSelected(e,t){"object"==typeof this.result[t]?(this.result[t]=[],this.selectedTitleObj[t]=e[0].title):(this.result[t]="",this.selectedTitleObj[t]=e[0].title);for(let i=0;i<e.length;i++)e[i].isSelected=0==i},sortTap(e,t,i){this.independence?(this.independenceObj[this.selectedKey]=t[e].value,this.result=this.independenceObj):(this.selectedObj[i]=t[e].value,this.result=this.selectedObj,this.selectedTitleObj[i]=t[e].title);for(let l=0;l<t.length;l++)t[l].isSelected=e==l;let s={result:this.result,titles:this.selectedTitleObj,isReset:!1};this.$emit("confirm",s)},sureClick(){let e={};for(let i in this.result)Array.isArray(this.result[i])?e[i]=this.result[i].filter((e=>""!==e)):e[i]=this.result[i];let t={result:e,titles:this.selectedTitleObj,isReset:!1};this.$emit("confirm",t)},resetClick(e,t){this.resetSelected(e,t)}}};const i=e._export_sfc(t,[["render",function(t,i,s,l,d,n){return e.e$1({a:d.menuIndex==d.index},d.menuIndex==d.index?{b:e.f(s.menuList,((t,i,l)=>e.e$1({a:t.isSort},t.isSort?{b:e.f(d.selectDetailList,((i,l,c)=>({a:e.t(i.title),b:l,c:e.n(i.isSelected?"filter-content-list-item-active":"filter-content-list-item-default"),d:i.isSelected?s.themeColor:"#666666",e:e.o((e=>n.sortTap(l,d.selectDetailList,t.key)),l)})))}:e.e$1({c:t.detailTitle&&t.detailTitle.length},t.detailTitle&&t.detailTitle.length?{d:e.t(t.detailTitle)}:{},{e:e.f(d.selectDetailList,((i,l,c)=>({a:e.t(i.title),b:l,c:i.isSelected?s.themeColor:"#FFFFFF",d:i.isSelected?"#FFFFFF":"#666666",e:e.o((e=>n.itemTap(l,d.selectDetailList,t.isMutiple,t.key)),l)}))),f:e.o((e=>n.resetClick(d.selectDetailList,t.key)),i),g:s.themeColor,h:e.o(((...e)=>n.sureClick&&n.sureClick(...e)),i)}),{i:i})))}:{},{c:e.gei(t,"")})}]]);wx.createComponent(i);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/songlazy-sl-filter/sl-filter/filter-view.js.map
