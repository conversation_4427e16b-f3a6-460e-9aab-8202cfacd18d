"use strict";const e=require("../uni-data-pickerview/uni-data-picker.js"),t=require("../../../../common/vendor.js"),i={name:"UniDataPicker",emits:["popupopened","popupclosed","nodeclick","input","change","update:modelValue","inputclick"],mixins:[e.dataPicker],components:{DataPickerView:()=>"../uni-data-pickerview/uni-data-pickerview.js"},props:{options:{type:[Object,Array],default:()=>({})},popupTitle:{type:String,default:"请选择"},placeholder:{type:String,default:"请选择"},heightMobile:{type:String,default:""},readonly:{type:Boolean,default:!1},clearIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!0},split:{type:String,default:"/"},ellipsis:{type:Boolean,default:!0}},data:()=>({isOpened:!1,inputSelected:[]}),created(){this.$nextTick((()=>{this.load()}))},watch:{localdata:{handler(){this.load()},deep:!0}},methods:{clear(){this._dispatchEvent([])},onPropsChange(){this._treeData=[],this.selectedIndex=0,this.load()},load(){this.readonly?this._processReadonly(this.localdata,this.dataValue):this.isLocalData?(this.loadData(),this.inputSelected=this.selected.slice(0)):(this.isCloudDataList||this.isCloudDataTree)&&(this.loading=!0,this.getCloudDataValue().then((e=>{this.loading=!1,this.inputSelected=e})).catch((e=>{this.loading=!1,this.errorMessage=e})))},show(){this.isOpened=!0,setTimeout((()=>{this.$refs.pickerView.updateData({treeData:this._treeData,selected:this.selected,selectedIndex:this.selectedIndex})}),200),this.$emit("popupopened")},hide(){this.isOpened=!1,this.$emit("popupclosed")},handleInput(){this.readonly?this.$emit("inputclick"):this.show()},handleClose(e){this.hide()},onnodeclick(e){this.$emit("nodeclick",e)},ondatachange(e){this._treeData=this.$refs.pickerView._treeData},onchange(e){this.hide(),this.$nextTick((()=>{this.inputSelected=e})),this._dispatchEvent(e)},_processReadonly(e,t){if(e.findIndex((e=>e.children))>-1){let e;return Array.isArray(t)?(e=t[t.length-1],"object"==typeof e&&e.value&&(e=e.value)):e=t,void(this.inputSelected=this._findNodePath(e,this.localdata))}if(!this.hasValue)return void(this.inputSelected=[]);let i=[];if(Array.isArray(t))for(let n=0;n<t.length;n++){var a=t[n],l=e.find((e=>e.value==a));l&&i.push(l)}else{let a=e.find((e=>e.value==t));a&&i.push(a)}i.length&&(this.inputSelected=i)},_filterForArray(e,t){var i=[];for(let n=0;n<t.length;n++){var a=t[n],l=e.find((e=>e.value==a));l&&i.push(l)}return i},_dispatchEvent(e){let t={};if(e.length){for(var i=new Array(e.length),a=0;a<e.length;a++)i[a]=e[a].value;t=e[e.length-1]}else t.value="";this.formItem&&this.formItem.setValue(t.value),this.$emit("input",t.value),this.$emit("update:modelValue",t.value),this.$emit("change",{detail:{value:e}})}}};if(!Array){(t.resolveComponent("uni-load-more")+t.resolveComponent("uni-icons")+t.resolveComponent("data-picker-view"))()}Math||((()=>"../../../uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../uni-icons/components/uni-icons/uni-icons.js"))();const a=t._export_sfc(i,[["render",function(e,i,a,l,n,o){return t.e$1({a:e.errorMessage},e.errorMessage?{b:t.t(e.errorMessage)}:e.loading&&!n.isOpened?{d:t.p({contentText:e.loadMore,status:"loading"})}:n.inputSelected.length?{f:t.f(n.inputSelected,((e,i,l)=>t.e$1({a:t.t(e.text),b:i<n.inputSelected.length-1},i<n.inputSelected.length-1?{c:t.t(a.split)}:{},{d:i})))}:{g:t.t(a.placeholder)},{c:e.loading&&!n.isOpened,e:n.inputSelected.length,h:a.clearIcon&&!a.readonly&&n.inputSelected.length},a.clearIcon&&!a.readonly&&n.inputSelected.length?{i:t.p({type:"clear",color:"#c0c4cc",size:"24"}),j:t.o(((...e)=>o.clear&&o.clear(...e)))}:{},{k:!(a.clearIcon&&n.inputSelected.length||a.readonly)},(a.clearIcon&&n.inputSelected.length||a.readonly,{}),{l:a.border?1:"",m:t.r("d",{options:a.options,data:n.inputSelected,error:e.errorMessage}),n:t.o(((...e)=>o.handleInput&&o.handleInput(...e))),o:n.isOpened},n.isOpened?{p:t.o(((...e)=>o.handleClose&&o.handleClose(...e)))}:{},{q:n.isOpened},n.isOpened?{r:t.t(a.popupTitle),s:t.o(((...e)=>o.handleClose&&o.handleClose(...e))),t:t.sr("pickerView","90a8cd50-2"),v:t.o(o.onchange),w:t.o(o.ondatachange),x:t.o(o.onnodeclick),y:t.o((t=>e.dataValue=t)),z:t.p({localdata:e.localdata,preload:e.preload,collection:e.collection,field:e.field,orderby:e.orderby,where:e.where,"step-searh":e.stepSearh,"self-field":e.selfField,"parent-field":e.parentField,"managed-mode":!0,map:e.map,ellipsis:a.ellipsis,modelValue:e.dataValue})}:{},{A:t.gei(e,"")})}]]);wx.createComponent(a);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.js.map
