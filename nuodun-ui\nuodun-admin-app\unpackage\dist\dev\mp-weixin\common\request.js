"use strict";const e=require("./vendor.js"),n=require("../api/request.js"),o=require("../config/environment.js");exports.request=function(e){return n.request({url:e.url,method:e.method||"GET",data:e.param,params:e.params,header:e.header,noToken:e.noToken,isLoading:e.isLoading,loadingTitle:e.loadingTitle})},exports.upload=function(t){const{url:i="/system/fileRecord/uploadByImg",name:r="file",filePath:a="",formData:d={},fileType:s="image",header:u=n.getAuthHeader(),isLoading:l=!0,loadingTitle:f="上传中...",success:c=null,fail:m=null,complete:p=null}=t;if(l&&e.index.showLoading(f),!a){l&&e.index.hideLoading();const n=new Error("未提供文件路径");return"function"==typeof m&&m(n),"function"==typeof p&&p(),Promise.reject(n)}return e.index.uploadFile({url:o.environment.baseUrl+i,filePath:a,name:r,formData:d,header:u,fileType:s,success:n=>{let o;l&&e.index.hideLoading();try{o=JSON.parse(n.data)}catch(t){e.index.__f__("error","at common/request.js:76","解析上传返回数据失败:",t),o=n.data}200===n.statusCode?"function"==typeof c&&c(o):"function"==typeof m&&m(o)},fail:n=>{e.index.__f__("error","at common/request.js:93","上传请求失败:",n),l&&e.index.hideLoading(),"function"==typeof m&&m(n)},complete:()=>{"function"==typeof p&&p()}})};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/request.js.map
