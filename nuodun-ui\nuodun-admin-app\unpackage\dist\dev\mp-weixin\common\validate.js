"use strict";const t=require("./toast.js");exports.checkIdCard=function(e,s=!0){return!!/(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/.test(e)||(s&&t.toast.show("身份证格式错误"),!1)},exports.checkPhone=function(e,s=!0){return!!/^1[1-9]\d{9}$/.test(e)||(s&&t.toast.show("手机号格式错误"),!1)},exports.checkPwd=function(e,s=!0){if(/^(?=.*[0-9]|.*[a-z]|.*[A-Z]|.*[^a-zA-Z0-9]).{6,20}$/.test(e)){let t=0;if(/[0-9]/.test(e)&&t++,/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,t>=2)return!0}return s&&t.toast.show("密码长度需为6-20位，且必须包含数字、字母、特殊字符中的至少两种"),!1};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/validate.js.map
