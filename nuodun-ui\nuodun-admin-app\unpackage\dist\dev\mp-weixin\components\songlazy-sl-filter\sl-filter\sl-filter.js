"use strict";const t=require("../../../common/vendor.js"),e={components:{popupLayer:()=>"./popup-layer.js",slFilterView:()=>"./filter-view.js"},props:{menuList:{type:Array,default:()=>[]},themeColor:{type:String,default:()=>"#000000"},color:{type:String,default:()=>"#666666"},independence:{type:Boolean,default:!1},isTransNav:{type:Boolean,default:!1},navHeight:{type:Number,default:0},topFixed:{type:Boolean,default:!1}},computed:{itemWidth:()=>"calc(100%/2)",menuListTemp:{get(){return this.getMenuListTemp()},set:t=>t}},onReady:function(){let t=[],e=[],i={};for(let s=0;s<this.menuList.length;s++)t.push({isActive:!1}),i[this.menuList[s].key]=this.menuList[s].title,this.menuList[s].reflexTitle&&this.menuList[s].defaultSelectedIndex>-1?e.push({title:this.menuList[s].detailList[this.menuList[s].defaultSelectedIndex].title,key:this.menuList[s].key}):e.push({title:this.menuList[s].title,key:this.menuList[s].key});this.statusList=t,this.titleList=e,this.tempTitleObj=i},data:()=>({down:"sl-down",up:"sl-up",tabHeight:50,statusList:[],selectedIndex:"",titleList:[],tempTitleObj:{}}),methods:{getMenuListTemp(){let t=this.menuList;for(let e=0;e<t.length;e++){let i=t[e];for(let t=0;t<i.detailList.length;t++){let e=i.detailList[t];e.isSelected=0==t}}return t},resetAllSelect(t){this.$refs.slFilterView.resetAllSelect((function(e){t(e)}))},resetSelectToDefault(t){this.$refs.slFilterView.resetSelectToDefault((function(e){t(e)}))},resetMenuList(t){this.menuList=t,this.$emit("update:menuList",t),this.$forceUpdate(),this.$refs.slFilterView.resetMenuList(t)},showMenuClick(t){this.selectedIndex=t,1==this.statusList[t].isActive?(this.$refs.popupRef.close(),this.statusList[t].isActive=!1):(this.menuTabClick(t),this.$refs.popupRef.show())},menuTabClick(t){this.$refs.slFilterView.menuTabClick(t);for(let e=0;e<this.statusList.length;e++)this.statusList[e].isActive=t==e},filterResult(t){let e=t.result,i=t.titles;if(this.independence){if(!this.menuList[this.selectedIndex].isMutiple||this.menuList[this.selectedIndex].isSort){let t="";for(let i=0;i<this.menuList[this.selectedIndex].detailList.length;i++){let s=this.menuList[this.selectedIndex].detailList[i];s.value==e[this.menuList[this.selectedIndex].key]&&(t=s.title)}this.menuList[this.selectedIndex].reflexTitle&&(this.titleList[this.selectedIndex].title=t)}}else{for(let t in i)Array.isArray(i[t])||(this.tempTitleObj[t]=i[t]);for(let t in this.tempTitleObj)for(let e=0;e<this.titleList.length;e++)this.titleList[e].key==t&&this.menuList[e]&&this.menuList[e].reflexTitle&&(this.titleList[e].title=this.tempTitleObj[t])}this.$refs.popupRef.close(),t.isReset||this.$emit("result",e)},close(){for(let t=0;t<this.statusList.length;t++)this.statusList[t].isActive=!1}}};if(!Array){(t.resolveComponent("sl-filter-view")+t.resolveComponent("popup-layer"))()}const i=t._export_sfc(e,[["render",function(e,i,s,l,n,h){return{a:t.f(n.titleList,((e,i,s)=>({a:t.t(e.title),b:t.n(n.statusList[i].isActive?n.up:n.down),c:i,d:t.o((t=>h.showMenuClick(i)),i)}))),b:s.color,c:h.itemWidth,d:t.n(s.topFixed?"select-tab-fixed-top":"select-tab"),e:n.tabHeight+"px",f:n.tabHeight+1+"px",g:t.o(h.filterResult),h:t.p({independence:s.independence,themeColor:s.themeColor,menuList:h.menuListTemp}),i:t.sr("popupRef","5aab3a25-0"),j:t.o(h.close),k:t.p({direction:"bottom",isTransNav:s.isTransNav,navHeight:s.navHeight,tabHeight:n.tabHeight}),l:t.gei(e,"")}}]]);wx.createComponent(i);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/songlazy-sl-filter/sl-filter/sl-filter.js.map
