"use strict";const e=require("../../vendor.js"),o={name:"DictTag",props:{options:{type:Array,default:null},value:[Number,String,Array],showTag:{type:Boolean,default:!1},maxLength:{type:Number,default:0}},computed:{values(){return null!==this.value&&void 0!==this.value?Array.isArray(this.value)?this.value:[String(this.value)]:[]}},methods:{getStatusLabelStyle(o){const a={padding:"4rpx 16rpx",fontSize:"24rpx",borderRadius:"8rpx",fontWeight:"500",letterSpacing:"1rpx",textAlign:"center",display:"inline-block"};if(o.elTagClass)try{const e="string"==typeof o.elTagClass?JSON.parse(o.elTagClass):o.elTagClass;return{...a,...e}}catch(t){e.index.__f__("error","at common/dict/DictTag/index.vue:67","解析elTagClass失败:",t)}if(o.elTagType){const e={success:{backgroundColor:"#f6ffed",color:"#52c41a"},info:{backgroundColor:"#e6f7ff",color:"#1890ff"},warning:{backgroundColor:"#fff2e8",color:"#fa8c16"},danger:{backgroundColor:"#fff1f0",color:"#f5222d"}};if(e[o.elTagType])return{...a,...e[o.elTagType]}}const r={0:{backgroundColor:"#f0f0f0",color:"#666"},1:{backgroundColor:"#e6f7ff",color:"#1890ff"},2:{backgroundColor:"#f6ffed",color:"#52c41a"},3:{backgroundColor:"#fff2e8",color:"#fa8c16"},4:{backgroundColor:"#fff1f0",color:"#f5222d"}},l=r[o.value]||r[0];return{...a,...l}}}};const a=e._export_sfc(o,[["render",function(o,a,r,l,t,n){return{a:e.f(r.options,((o,a,l)=>e.e$1({a:n.values.includes(o.value)},n.values.includes(o.value)?{b:e.t(r.maxLength&&o.label.length>r.maxLength?o.label.slice(0,r.maxLength)+"...":o.label),c:a,d:a,e:e.s(r.showTag?n.getStatusLabelStyle(o):{})}:{}))),b:e.gei(o,"")}}],["__scopeId","data-v-03734b74"]]);wx.createComponent(a);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/dict/DictTag/index.js.map
